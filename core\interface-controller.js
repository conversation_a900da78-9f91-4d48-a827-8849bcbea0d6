/**
 * @file interface-controller.js - 统一界面控制器（Observer模式增强版）
 * @description 合并UI管理和事件处理功能的统一控制器，支持Observer模式自动UI同步
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.1.0
 */

/**
 * @class InterfaceController - 统一界面控制器（Observer模式增强版）
 * @description 管理所有UI操作和用户交互事件的统一控制器，支持自动UI同步
 */
class InterfaceController {
    /**
     * @function constructor - 构造函数
     * @description 初始化界面控制器并设置Observer模式订阅
     * @param {OTAOrderApp} app - 主应用实例
     * @param {AppState} appState - 应用状态实例
     */
    constructor(app, appState) {
        this.app = app;
        this.appState = appState;
        
        // UI状态管理
        this.loadingCount = 0;
        this.currentModal = null;
        
        // 事件管理
        this.boundEvents = new Map();
        this.eventListeners = new Map();
        
        // Observer模式：订阅AppState事件
        this.observerIds = new Map(); // 存储观察者ID，用于清理
        this.setupObservers();
        
        logger.info('InterfaceController', '界面控制器初始化完成（Observer模式已启用）');
    }

    // #region Observer模式支持

    /**
     * @function setupObservers - 设置Observer模式订阅
     * @description 订阅AppState的各种事件，实现自动UI同步
     */
    setupObservers() {
        // 订阅用户登录事件
        const userLoginId = this.appState.subscribe('userLogin', (eventData) => {
            this.onUserLogin(eventData);
        }, { priority: 10 });
        this.observerIds.set('userLogin', userLoginId);

        // 订阅用户登出事件
        const userLogoutId = this.appState.subscribe('userLogout', (eventData) => {
            this.onUserLogout(eventData);
        }, { priority: 10 });
        this.observerIds.set('userLogout', userLogoutId);

        // 订阅用户切换事件
        const userSwitchId = this.appState.subscribe('userSwitch', (eventData) => {
            this.onUserSwitch(eventData);
        }, { priority: 10 });
        this.observerIds.set('userSwitch', userSwitchId);

        // 订阅数据更新事件
        const dataUpdateId = this.appState.subscribe('dataUpdate', (eventData) => {
            this.onDataUpdate(eventData);
        }, { priority: 5 });
        this.observerIds.set('dataUpdate', dataUpdateId);

        // 订阅认证状态变更事件
        const authStateChangeId = this.appState.subscribe('authStateChange', (eventData) => {
            this.onAuthStateChange(eventData);
        }, { priority: 10 });
        this.observerIds.set('authStateChange', authStateChangeId);

        logger.info('InterfaceController', 'Observer订阅已设置', {
            subscribedEvents: Array.from(this.observerIds.keys())
        });
    }

    /**
     * @function onUserLogin - 处理用户登录事件
     * @description 用户登录时的UI响应
     * @param {object} eventData - 事件数据
     */
    onUserLogin(eventData) {
        logger.info('InterfaceController', '响应用户登录事件', eventData.data);
        
        // 隐藏登录模态框
        this.hideLoginModal();
        
        // 更新连接状态
        this.updateConnectionStatus();
        
        // 显示成功消息
        if (eventData.data.isFirstLogin) {
            this.showSuccess(`欢迎登录，${eventData.data.userInfo.email}`);
        } else {
            this.showInfo('用户状态已恢复');
        }
    }

    /**
     * @function onUserLogout - 处理用户登出事件
     * @description 用户登出时的UI响应
     * @param {object} eventData - 事件数据
     */
    onUserLogout(eventData) {
        logger.info('InterfaceController', '响应用户登出事件', eventData.data);
        
        // 清空UI显示
        this.clearUIDisplays();
        
        // 显示登录模态框
        this.showLoginModal();
        
        // 更新连接状态
        this.updateConnectionStatus();
        
        // 显示登出消息
        this.showInfo('已安全登出');
    }

    /**
     * @function onUserSwitch - 处理用户切换事件
     * @description 用户切换时的UI响应
     * @param {object} eventData - 事件数据
     */
    onUserSwitch(eventData) {
        logger.info('InterfaceController', '响应用户切换事件', eventData.data);
        
        // 清空UI显示
        this.clearUIDisplays();
        
        // 显示切换消息
        this.showInfo(`用户已切换：${eventData.data.oldUser} → ${eventData.data.newUser}`);
        
        // 延迟更新UI选择器（等待数据加载）
        setTimeout(() => {
            this.updateUISelectors();
        }, 1000);
    }

    /**
     * @function onDataUpdate - 处理数据更新事件
     * @description 系统数据更新时的UI响应
     * @param {object} eventData - 事件数据
     */
    onDataUpdate(eventData) {
        const { key, newData } = eventData.data;
        logger.debug('InterfaceController', '响应数据更新事件', { key, dataLength: newData?.length });
        
        // 根据数据类型更新对应的UI选择器
        switch (key) {
            case 'backendUsers':
                this.updateBackendUserSelector();
                break;
            case 'subCategories':
                this.updateSubCategorySelector();
                break;
            case 'carTypes':
                this.updateCarTypeSelector();
                break;
            default:
                // 其他数据类型的处理
                logger.debug('InterfaceController', '未知数据类型更新', { key });
        }
    }

    /**
     * @function onAuthStateChange - 处理认证状态变更事件
     * @description 认证状态变更时的UI响应
     * @param {object} eventData - 事件数据
     */
    onAuthStateChange(eventData) {
        const { type, authenticated } = eventData.data;
        logger.debug('InterfaceController', '响应认证状态变更', { type, authenticated });
        
        // 更新连接状态指示器
        this.updateConnectionStatus();
        
        // 根据认证状态调整UI
        if (authenticated) {
            // 已认证：确保登录模态框隐藏
            this.hideLoginModal();
        } else {
            // 未认证：显示登录模态框
            this.showLoginModal();
        }
    }

    /**
     * @function clearObservers - 清理Observer订阅
     * @description 清理所有Observer订阅，防止内存泄漏
     */
    clearObservers() {
        this.observerIds.forEach((observerId, event) => {
            this.appState.unsubscribe(event, observerId);
        });
        this.observerIds.clear();
        logger.debug('InterfaceController', 'Observer订阅已清理');
    }

    // #endregion

    // #region UI管理功能

    /**
     * @function initializeUI - 初始化UI组件
     * @description 初始化所有UI组件和状态
     */
    initializeUI() {
        logger.info('InterfaceController', '开始初始化UI组件');

        // 显示登录模态框（如果未登录）
        if (!this.appState.token) {
            this.showLoginModal();
        }

        // 初始化状态指示器
        this.updateConnectionStatus();

        // 初始化文件上传区域
        this.initializeFileUpload();

        // 初始化地址搜索组件
        this.initializeAddressSearchComponents();

        logger.debug('InterfaceController', 'UI组件初始化完成');
    }

    /**
     * @function initializeFileUpload - 初始化文件上传区域
     * @description 设置文件上传区域的样式和提示
     */
    initializeFileUpload() {
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            // 设置拖拽区域样式
            uploadArea.style.cssText = `
                border: 2px dashed #ccc;
                border-radius: 10px;
                text-align: center;
                padding: 20px;
                margin: 10px 0;
                cursor: pointer;
                transition: border-color 0.3s ease;
            `;
            
            // 添加提示文本
            if (!uploadArea.querySelector('.upload-hint')) {
                const hint = document.createElement('div');
                hint.className = 'upload-hint';
                hint.innerHTML = '📁 点击选择文件或拖拽图片到此处';
                hint.style.cssText = 'color: #666; font-size: 14px;';
                uploadArea.appendChild(hint);
            }
        }
    }

    /**
     * @function initializeAddressSearchComponents - 初始化地址搜索组件
     * @description 为地址输入框添加搜索功能组件
     */
    initializeAddressSearchComponents() {
        const addressInputs = document.querySelectorAll('input[data-address-search]');
        addressInputs.forEach(input => {
            this.setupAddressSearchForInput(input);
        });
    }

    /**
     * @function setupAddressSearchForInput - 为输入框设置地址搜索
     * @description 为特定输入框添加地址搜索功能
     * @param {HTMLElement} input - 输入框元素
     */
    setupAddressSearchForInput(input) {
        // 创建搜索建议容器
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'address-suggestions';
        suggestionsContainer.style.display = 'none';
        
        // 插入到输入框后面
        input.parentNode.insertBefore(suggestionsContainer, input.nextSibling);
        
        // 存储引用
        input.suggestionsContainer = suggestionsContainer;
    }

    /**
     * @function showLoginModal - 显示登录模态框
     * @description 显示用户登录界面，自动填充默认值和上次登录信息
     */
    showLoginModal() {
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.style.display = 'block';
            this.currentModal = modal;
            
            // 设置登录表单的默认值
            this.setLoginFormDefaults();
            
            logger.debug('InterfaceController', '显示登录模态框');
        }
    }

    /**
     * @function setLoginFormDefaults - 设置登录表单默认值
     * @description 设置邮箱和密码的默认值，优先使用上次登录信息
     */
    setLoginFormDefaults() {
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        
        if (emailInput && passwordInput) {
            // 尝试从localStorage获取上次登录信息
            const lastLogin = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.LAST_LOGIN);
            
            if (lastLogin) {
                try {
                    const loginInfo = JSON.parse(lastLogin);
                    emailInput.value = loginInfo.email || SYSTEM_CONFIG.DEFAULT_LOGIN.email;
                    // 出于安全考虑，不保存密码，使用配置的默认密码
                    passwordInput.value = SYSTEM_CONFIG.DEFAULT_LOGIN.password;
                    
                    logger.debug('InterfaceController', '使用上次登录信息', { 
                        email: loginInfo.email 
                    });
                } catch (error) {
                    logger.warn('InterfaceController', '解析上次登录信息失败，使用默认值', error);
                    this.setDefaultLoginValues(emailInput, passwordInput);
                }
            } else {
                // 使用配置文件中的默认值
                this.setDefaultLoginValues(emailInput, passwordInput);
            }
        }
    }

    /**
     * @function setDefaultLoginValues - 设置默认登录值
     * @description 设置配置文件中定义的默认登录值
     * @param {HTMLElement} emailInput - 邮箱输入框
     * @param {HTMLElement} passwordInput - 密码输入框
     */
    setDefaultLoginValues(emailInput, passwordInput) {
        emailInput.value = SYSTEM_CONFIG.DEFAULT_LOGIN.email;
        passwordInput.value = SYSTEM_CONFIG.DEFAULT_LOGIN.password;
        
        logger.debug('InterfaceController', '使用配置默认登录值', {
            email: SYSTEM_CONFIG.DEFAULT_LOGIN.email
        });
    }

    /**
     * @function hideLoginModal - 隐藏登录模态框
     * @description 隐藏用户登录界面
     */
    hideLoginModal() {
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.style.display = 'none';
            this.currentModal = null;
            logger.debug('InterfaceController', '隐藏登录模态框');
        }
    }

    /**
     * @function showLoading - 显示加载状态
     * @description 显示加载指示器和消息
     * @param {string} message - 加载消息
     */
    showLoading(message = '加载中...') {
        this.loadingCount++;
        
        const loadingDiv = document.getElementById('loadingIndicator') || this.createLoadingIndicator();
        const messageDiv = loadingDiv.querySelector('.loading-message');
        
        if (messageDiv) {
            messageDiv.textContent = message;
        }
        
        loadingDiv.style.display = 'flex';
        logger.debug('InterfaceController', '显示加载状态', { message, count: this.loadingCount });
    }

    /**
     * @function updateLoadingMessage - 更新加载消息
     * @description 更新加载消息而不增加计数器
     * @param {string} message - 新的加载消息
     */
    updateLoadingMessage(message) {
        const loadingDiv = document.getElementById('loadingIndicator');
        if (loadingDiv) {
            const messageDiv = loadingDiv.querySelector('.loading-message');
            if (messageDiv) {
                messageDiv.textContent = message;
            }
            logger.debug('InterfaceController', '更新加载消息', { message, count: this.loadingCount });
        }
    }

    /**
     * @function hideLoading - 隐藏加载状态
     * @description 隐藏加载指示器
     */
    hideLoading() {
        this.loadingCount = Math.max(0, this.loadingCount - 1);
        
        if (this.loadingCount === 0) {
            const loadingDiv = document.getElementById('loadingIndicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
            logger.debug('InterfaceController', '隐藏加载状态');
        }
    }

    /**
     * @function createLoadingIndicator - 创建加载指示器
     * @description 动态创建支持进度条的加载指示器元素
     * @returns {HTMLElement} 加载指示器元素
     */
    createLoadingIndicator() {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loadingIndicator';
        loadingDiv.className = 'loading-overlay';
        loadingDiv.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-message">加载中...</div>
                <div class="loading-progress-container" style="display: none;">
                    <div class="loading-progress-bar-bg">
                        <div class="loading-progress-bar" style="width: 0%;"></div>
                    </div>
                    <div class="loading-progress-text">0%</div>
                </div>
                <div class="loading-performance-info" style="display: none;">
                    <small class="performance-text"></small>
                </div>
            </div>
        `;
        
        // 添加样式
        loadingDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;
        
        // 添加进度条样式
        const style = document.createElement('style');
        style.textContent = `
            .loading-content {
                background: white;
                padding: 30px;
                border-radius: 10px;
                text-align: center;
                min-width: 300px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            }
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .loading-message {
                font-size: 16px;
                color: #333;
                margin-bottom: 15px;
            }
            .loading-progress-container {
                margin: 15px 0;
            }
            .loading-progress-bar-bg {
                width: 100%;
                height: 8px;
                background-color: #e9ecef;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 8px;
            }
            .loading-progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #007bff, #28a745);
                border-radius: 4px;
                transition: width 0.3s ease;
            }
            .loading-progress-text {
                font-size: 14px;
                color: #666;
                font-weight: 500;
            }
            .loading-performance-info {
                margin-top: 10px;
            }
            .performance-text {
                font-size: 12px;
                color: #888;
                display: block;
                margin-top: 5px;
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(loadingDiv);
        return loadingDiv;
    }

    /**
     * @function showLoadingWithProgress - 显示带进度的加载状态
     * @description 显示支持进度条的加载指示器
     * @param {string} message - 加载消息
     * @param {boolean} showProgress - 是否显示进度条
     */
    showLoadingWithProgress(message = '加载中...', showProgress = false) {
        this.loadingCount++;
        
        const loadingDiv = document.getElementById('loadingIndicator') || this.createLoadingIndicator();
        const messageDiv = loadingDiv.querySelector('.loading-message');
        const progressContainer = loadingDiv.querySelector('.loading-progress-container');
        
        if (messageDiv) {
            messageDiv.textContent = message;
        }
        
        if (progressContainer) {
            progressContainer.style.display = showProgress ? 'block' : 'none';
        }
        
        loadingDiv.style.display = 'flex';
        logger.debug('InterfaceController', '显示带进度的加载状态', { 
            message, 
            showProgress, 
            count: this.loadingCount 
        });
    }

    /**
     * @function updateLoadingProgress - 更新加载进度
     * @description 更新加载进度条和相关信息
     * @param {number} percentage - 进度百分比 (0-100)
     * @param {string} progressText - 进度文本
     * @param {string} performanceInfo - 性能信息
     */
    updateLoadingProgress(percentage = 0, progressText = '', performanceInfo = '') {
        const loadingDiv = document.getElementById('loadingIndicator');
        if (!loadingDiv) return;

        const progressBar = loadingDiv.querySelector('.loading-progress-bar');
        const progressTextEl = loadingDiv.querySelector('.loading-progress-text');
        const performanceEl = loadingDiv.querySelector('.performance-text');
        const performanceContainer = loadingDiv.querySelector('.loading-performance-info');

        if (progressBar) {
            progressBar.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
        }

        if (progressTextEl && progressText) {
            progressTextEl.textContent = progressText;
        }

        if (performanceEl && performanceInfo) {
            performanceEl.textContent = performanceInfo;
            if (performanceContainer) {
                performanceContainer.style.display = 'block';
            }
        }

        logger.debug('InterfaceController', '更新加载进度', {
            percentage,
            progressText,
            performanceInfo
        });
    }

    /**
     * @function addPerformanceMonitor - 添加性能监控界面
     * @description 在页面中添加性能监控面板
     */
    addPerformanceMonitor() {
        // 检查是否已存在
        if (document.getElementById('performanceMonitor')) {
            return;
        }

        const monitor = document.createElement('div');
        monitor.id = 'performanceMonitor';
        monitor.innerHTML = `
            <div class="performance-header">
                <h4>性能监控</h4>
                <button class="performance-toggle" title="切换显示">📊</button>
            </div>
            <div class="performance-content">
                <div class="performance-stats">
                    <div class="stat-item">
                        <label>平均加载时间:</label>
                        <span class="avg-load-time">-</span>
                    </div>
                    <div class="stat-item">
                        <label>成功率:</label>
                        <span class="success-rate">-</span>
                    </div>
                    <div class="stat-item">
                        <label>缓存命中率:</label>
                        <span class="cache-hit-rate">-</span>
                    </div>
                </div>
                <div class="performance-actions">
                    <button class="btn-clear-cache">清理缓存</button>
                    <button class="btn-force-refresh">强制刷新</button>
                    <button class="btn-memory-optimize">内存优化</button>
                    <button class="btn-run-test">性能测试</button>
                </div>
            </div>
        `;

        // 添加样式
        monitor.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 280px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            font-size: 12px;
        `;

        // 添加CSS样式
        const style = document.createElement('style');
        style.textContent = `
            .performance-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: #f8f9fa;
                border-bottom: 1px solid #ddd;
                border-radius: 8px 8px 0 0;
            }
            .performance-header h4 {
                margin: 0;
                font-size: 14px;
                color: #333;
            }
            .performance-toggle {
                border: none;
                background: none;
                cursor: pointer;
                font-size: 16px;
            }
            .performance-content {
                padding: 12px;
            }
            .performance-stats {
                margin-bottom: 12px;
            }
            .stat-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 6px;
            }
            .stat-item label {
                color: #666;
            }
            .stat-item span {
                font-weight: 500;
                color: #333;
            }
            .performance-actions {
                display: flex;
                gap: 4px;
                flex-wrap: wrap;
            }
            .performance-actions button {
                flex: 1;
                padding: 4px 8px;
                border: 1px solid #ddd;
                background: #f8f9fa;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
                min-width: 60px;
            }
            .performance-actions button:hover {
                background: #e9ecef;
            }
            .performance-content.collapsed {
                display: none;
            }
        `;
        document.head.appendChild(style);

        // 绑定事件
        const toggleBtn = monitor.querySelector('.performance-toggle');
        const content = monitor.querySelector('.performance-content');
        const clearCacheBtn = monitor.querySelector('.btn-clear-cache');
        const forceRefreshBtn = monitor.querySelector('.btn-force-refresh');
        const memoryOptimizeBtn = monitor.querySelector('.btn-memory-optimize');
        const runTestBtn = monitor.querySelector('.btn-run-test');

        toggleBtn.addEventListener('click', () => {
            content.classList.toggle('collapsed');
        });

        clearCacheBtn.addEventListener('click', () => {
            this.appState.clearUserSpecificData();
            this.showSuccess('缓存已清理');
            this.updatePerformanceStats();
        });

        forceRefreshBtn.addEventListener('click', () => {
            this.appState.setForceRefreshFlag();
            this.showInfo('下次加载时将强制刷新');
        });

        memoryOptimizeBtn.addEventListener('click', async () => {
            logger.info('InterfaceController', '执行内存优化');
            
            try {
                // 使用性能优化器执行内存优化
                if (this.app.performanceOptimizer) {
                    this.app.performanceOptimizer.executeMemoryOptimization();
                } else {
                    this.appState.optimizeMemoryUsage();
                }
                
                this.updatePerformanceStats();
                this.showSuccess('内存优化完成');
            } catch (error) {
                logger.error('InterfaceController', '内存优化失败', error);
                this.showError('内存优化失败: ' + error.message);
            }
        });

        runTestBtn.addEventListener('click', async () => {
            logger.info('InterfaceController', '运行性能测试');
            
            try {
                this.showLoading('正在执行性能测试...');
                
                if (this.app.performanceOptimizer) {
                    const testResults = await this.app.performanceOptimizer.runComprehensivePerformanceTest();
                    this.showPerformanceTestResults(testResults);
                } else {
                    this.showError('性能优化器未初始化');
                }
                
            } catch (error) {
                logger.error('InterfaceController', '性能测试失败', error);
                this.showError('性能测试失败: ' + error.message);
            } finally {
                this.hideLoading();
            }
        });

        document.body.appendChild(monitor);
        
        // 初始更新统计
        this.updatePerformanceStats();
        
        logger.info('InterfaceController', '性能监控面板已添加');
    }

    /**
     * @function updatePerformanceStats - 更新性能统计显示
     * @description 更新性能监控面板中的统计数据
     */
    updatePerformanceStats() {
        const monitor = document.getElementById('performanceMonitor');
        if (!monitor) return;

        // 如果有性能优化器，使用优化器的统计数据
        if (this.app.performanceOptimizer) {
            this.app.performanceOptimizer.updatePerformanceUI();
        } else {
            // 使用原有的统计逻辑
            const insights = this.appState.getLoadingPerformanceInsights();
            const cacheInfo = this.appState.getCacheInfo();

            const avgLoadTimeEl = monitor.querySelector('.avg-load-time');
            const successRateEl = monitor.querySelector('.success-rate');
            const cacheHitRateEl = monitor.querySelector('.cache-hit-rate');

            if (avgLoadTimeEl) {
                avgLoadTimeEl.textContent = insights.averageLoadTime ? 
                    `${insights.averageLoadTime}ms` : '-';
            }

            if (successRateEl) {
                successRateEl.textContent = insights.averageSuccessRate ? 
                    `${insights.averageSuccessRate}%` : '-';
            }

            if (cacheHitRateEl) {
                const hitRate = cacheInfo.currentUserData ? '85%' : '0%'; // 简化计算
                cacheHitRateEl.textContent = hitRate;
            }
        }

        logger.debug('InterfaceController', '性能统计已更新');
    }

    /**
     * @function showPerformanceTestResults - 显示性能测试结果
     * @description 显示详细的性能测试结果
     * @param {object} testResults - 测试结果对象
     */
    showPerformanceTestResults(testResults) {
        const resultMessage = `性能测试完成！
总体评分：${testResults.overallScore}/100
测试时长：${testResults.duration}ms
执行的测试：${Object.keys(testResults.tests).length}项

详细结果：
${Object.entries(testResults.tests).map(([testName, result]) => 
    `• ${testName}: ${Math.round(result.score || 0)}/100`
).join('\n')}

${testResults.recommendations.length > 0 ? 
    '\n优化建议：\n' + testResults.recommendations.map(rec => 
        `• ${rec.message}`
    ).join('\n') : ''}`;

        // 显示详细的测试结果
        if (window.NotificationManager) {
            window.NotificationManager.show(
                testResults.overallScore >= 80 ? 'success' : 
                testResults.overallScore >= 60 ? 'warning' : 'error',
                '性能测试结果',
                resultMessage,
                10000, // 10秒显示时间
                { showDetails: true }
            );
        } else {
            alert(resultMessage);
        }

        // 更新性能统计
        this.updatePerformanceStats();

        logger.info('InterfaceController', '性能测试结果已显示', {
            score: testResults.overallScore,
            duration: testResults.duration,
            testsCount: Object.keys(testResults.tests).length
        });
    }

    /**
     * @function updateConnectionStatus - 更新连接状态
     * @description 更新所有连接状态指示器，包括本地模式状态
     */
    updateConnectionStatus() {
        // 更新用户信息显示
        const userInfo = this.appState.userInfo;
        const userInfoElement = document.getElementById('userInfo');
        const logoutBtn = document.getElementById('logoutBtn');
        
        // 检查本地模式
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            if (userInfoElement) {
                userInfoElement.textContent = '本地模式 - 无需登录';
            }
            if (logoutBtn) {
                logoutBtn.style.display = 'none';
            }
            this.updateLocalModeIndicator(true);
        } else {
            if (userInfo && userInfoElement) {
                userInfoElement.textContent = `欢迎，${userInfo.name || userInfo.email}`;
            }
            if (logoutBtn) {
                logoutBtn.style.display = 'inline-block';
            }
            this.updateLocalModeIndicator(false);
        }

        // 更新LLM状态 - 检查LLM服务是否可用
        if (window.llmService) {
            // 如果LLM服务已加载，显示检测中状态
            this.updateLLMStatusUI({
                gemini: { status: 'checking', message: '检测中...' }
            });
        } else {
            // 如果LLM服务未加载，显示未初始化状态
            this.updateLLMStatusUI({
                gemini: { status: 'disconnected', message: 'LLM服务未初始化' }
            });
        }
    }

    /**
     * @function updateLocalModeIndicator - 更新本地模式指示器
     * @description 更新本地模式状态指示器
     * @param {boolean} isLocalMode - 是否为本地模式
     */
    updateLocalModeIndicator(isLocalMode) {
        const indicator = document.getElementById('localModeIndicator');
        const light = document.getElementById('localModeLight');
        const text = document.getElementById('localModeText');
        
        if (!indicator || !light || !text) return;
        
        if (isLocalMode) {
            light.className = 'status-light connected';
            text.textContent = '本地模式';
            indicator.title = '当前使用本地数据，无需登录';
        } else {
            light.className = 'status-light disconnected';
            text.textContent = '在线模式';
            indicator.title = '当前使用在线API数据';
        }
    }

    /**
     * @function updateLLMStatusUI - 更新LLM状态UI
     * @description 更新LLM服务状态指示器
     * @param {object} status - 状态对象
     */
    updateLLMStatusUI(status) {
        if (status.gemini) {
            this.updateStatusIndicator('geminiStatusIndicator', 'geminiStatusLight', 'geminiStatusText', status.gemini);
        }
    }

    /**
     * @function updateStatusIndicator - 更新状态指示器
     * @description 更新特定状态指示器的显示
     * @param {string} indicatorId - 指示器ID
     * @param {string} lightId - 状态灯ID
     * @param {string} textId - 状态文本ID
     * @param {object} status - 状态信息
     */
    updateStatusIndicator(indicatorId, lightId, textId, status) {
        const indicator = document.getElementById(indicatorId);
        const light = document.getElementById(lightId);
        const text = document.getElementById(textId);

        if (indicator && light && text) {
            // 移除所有状态类
            light.className = 'status-light';
            
            // 添加对应状态类
            switch (status.status) {
                case 'connected':
                    light.classList.add('connected');
                    break;
                case 'error':
                    light.classList.add('error');
                    break;
                case 'checking':
                default:
                    light.classList.add('checking');
                    break;
            }
            
            text.textContent = status.message || '未知状态';
        }
    }

    /**
     * @function showError - 显示错误消息
     * @description 显示错误通知
     * @param {string} message - 错误消息
     */
    showError(message) {
        if (window.NotificationManager) {
            window.NotificationManager.showError(message);
        } else {
            alert('错误: ' + message);
        }
        logger.error('InterfaceController', '显示错误消息', { message });
    }

    /**
     * @function showSuccess - 显示成功消息
     * @description 显示成功通知
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        if (window.NotificationManager) {
            window.NotificationManager.showSuccess(message);
        } else {
            alert('成功: ' + message);
        }
        logger.success('InterfaceController', '显示成功消息', { message });
    }

    /**
     * @function showInfo - 显示信息消息
     * @description 显示信息通知
     * @param {string} message - 信息消息
     */
    showInfo(message) {
        if (window.NotificationManager) {
            window.NotificationManager.showInfo(message);
        } else {
            alert('信息: ' + message);
        }
        logger.info('InterfaceController', '显示信息消息', { message });
    }

    /**
     * @function showWarning - 显示警告消息
     * @description 显示警告通知
     * @param {string} message - 警告消息
     */
    showWarning(message) {
        if (window.NotificationManager) {
            window.NotificationManager.showWarning(message);
        } else {
            alert('警告: ' + message);
        }
        logger.warn('InterfaceController', '显示警告消息', { message });
    }

    /**
     * @function updateSessionStatus - 更新会话状态显示
     * @description 更新登录状态指示器和相关UI元素
     * @param {boolean} isLoggedIn - 是否已登录
     * @param {boolean} isPersistent - 是否为持久化会话
     */
    updateSessionStatus(isLoggedIn, isPersistent = false) {
        try {
            // 更新登录状态指示器
            const statusIndicator = document.getElementById('loginStatusIndicator');
            if (statusIndicator) {
                if (isLoggedIn) {
                    statusIndicator.className = 'status-indicator connected';
                    statusIndicator.textContent = isPersistent ? '已登录（已保存）' : '已登录';
                    statusIndicator.title = isPersistent
                        ? '登录状态已保存，下次访问将自动登录'
                        : '当前会话已登录';
                } else {
                    statusIndicator.className = 'status-indicator disconnected';
                    statusIndicator.textContent = '未登录';
                    statusIndicator.title = '点击登录';
                }
            }

            // 更新登录/登出按钮状态
            const loginBtn = document.getElementById('loginBtn');
            const logoutBtn = document.getElementById('logoutBtn');

            if (loginBtn) {
                loginBtn.style.display = isLoggedIn ? 'none' : 'inline-block';
            }

            if (logoutBtn) {
                logoutBtn.style.display = isLoggedIn ? 'inline-block' : 'none';
            }

            // 更新用户信息显示
            this.updateUserInfoDisplay(isLoggedIn);

            logger.debug('InterfaceController', '会话状态已更新', {
                isLoggedIn,
                isPersistent
            });

        } catch (error) {
            logger.error('InterfaceController', '更新会话状态失败', error);
        }
    }

    /**
     * @function prefillLoginEmail - 预填充登录邮箱
     * @description 在登录表单中预填充最后登录的邮箱
     * @param {string} email - 邮箱地址
     */
    prefillLoginEmail(email) {
        try {
            const emailInput = document.getElementById('email');
            if (emailInput && email) {
                emailInput.value = email;
                logger.debug('InterfaceController', '预填充登录邮箱', { email });
            }
        } catch (error) {
            logger.error('InterfaceController', '预填充邮箱失败', error);
        }
    }

    /**
     * @function updateUserInfoDisplay - 更新用户信息显示
     * @description 更新界面上的用户信息显示
     * @param {boolean} isLoggedIn - 是否已登录
     */
    updateUserInfoDisplay(isLoggedIn) {
        try {
            const userInfoElement = document.getElementById('userInfo');
            if (userInfoElement) {
                if (isLoggedIn && this.appState.userInfo) {
                    const userEmail = this.appState.userInfo.email || '未知用户';
                    userInfoElement.textContent = userEmail;
                    userInfoElement.style.display = 'block';
                } else {
                    userInfoElement.style.display = 'none';
                }
            }
        } catch (error) {
            logger.error('InterfaceController', '更新用户信息显示失败', error);
        }
    }

    /**
     * @function toggleSection - 切换区域显示
     * @description 显示或隐藏指定区域
     * @param {string} sectionId - 区域ID
     * @param {boolean} show - 是否显示
     */
    toggleSection(sectionId, show) {
        const section = document.getElementById(sectionId);
        if (section) {
            if (show) {
                section.classList.remove('hidden');
            } else {
                section.classList.add('hidden');
            }
            logger.debug('InterfaceController', `切换区域显示: ${sectionId}`, { show });
        }
    }

    /**
     * @function scrollToSection - 滚动到指定区域
     * @description 平滑滚动到指定区域
     * @param {string} sectionId - 区域ID
     */
    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
            logger.debug('InterfaceController', `滚动到区域: ${sectionId}`);
        }
    }

    // #endregion

    // #region 事件管理功能

    /**
     * @function bindEventListeners - 绑定事件监听器
     * @description 绑定所有UI元素的事件监听器
     */
    bindEventListeners() {
        logger.info('InterfaceController', '开始绑定事件监听器');

        // 登录表单事件
        this.bindLoginEvents();
        
        // 主要操作按钮事件
        this.bindMainActionEvents();
        
        // 文件上传事件
        this.bindFileUploadEvents();
        
        // LLM状态指示器事件
        this.bindLLMStatusEvents();
        
        // 其他操作按钮事件
        this.bindOtherActionEvents();
        
        // 手动编辑相关事件
        this.bindManualEditEvents();
        
        // Profile相关事件
        this.bindProfileEvents();

        logger.debug('InterfaceController', '事件监听器绑定完成');
    }

    /**
     * @function bindLoginEvents - 绑定登录相关事件
     * @description 绑定登录表单和登出按钮的事件，与增强登录处理器协作
     */
    bindLoginEvents() {
        // 检查是否已有增强登录处理器
        if (window.enhancedLoginHandler) {
            logger.info('InterfaceController', '检测到增强登录处理器，跳过重复绑定');

            // 只绑定登出按钮
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                this.addEventListener(logoutBtn, 'click', () => this.handleLogout());
            }
            return;
        }

        // 传统登录事件绑定（降级方案）
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            this.addEventListener(loginForm, 'submit', (e) => this.handleLogin(e));
            logger.info('InterfaceController', '使用传统登录事件绑定');
        }

        // 登出按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            this.addEventListener(logoutBtn, 'click', () => this.handleLogout());
        }
    }

    /**
     * @function bindMainActionEvents - 绑定主要操作事件
     * @description 绑定处理订单和创建订单按钮的事件
     */
    bindMainActionEvents() {
        // 处理订单按钮
        const processBtn = document.getElementById('processBtn');
        if (processBtn) {
            this.addEventListener(processBtn, 'click', () => this.handleProcessOrder());
        }

        // 创建订单按钮
        const createBtn = document.getElementById('createOrderBtn');
        if (createBtn) {
            this.addEventListener(createBtn, 'click', () => this.handleCreateOrders());
        }
    }

    /**
     * @function bindFileUploadEvents - 绑定文件上传事件
     * @description 绑定文件选择和拖拽上传的事件
     */
    bindFileUploadEvents() {
        // 文件上传
        const fileInput = document.getElementById('imageFile');
        if (fileInput) {
            this.addEventListener(fileInput, 'change', (e) => this.handleFileUpload(e));
        }

        // 拖拽上传
        const dropZone = document.getElementById('uploadArea');
        if (dropZone) {
            this.addEventListener(dropZone, 'dragover', (e) => this.handleDragOver(e));
            this.addEventListener(dropZone, 'drop', (e) => this.handleFileDrop(e));
            this.addEventListener(dropZone, 'click', () => this.handleUploadAreaClick());
        }
    }

    /**
     * @function bindLLMStatusEvents - 绑定LLM状态事件
     * @description 绑定LLM状态指示器的点击事件
     */
    bindLLMStatusEvents() {
        // Gemini状态指示器
        const geminiIndicator = document.getElementById('geminiStatusIndicator');
        if (geminiIndicator) {
            this.addEventListener(geminiIndicator, 'click', () => this.handleLLMStatusClick('gemini'));
        }
    }

    /**
     * @function bindOtherActionEvents - 绑定其他操作事件
     * @description 绑定编辑、刷新、导出等按钮的事件
     */
    bindOtherActionEvents() {
        // 编辑按钮
        const editBtn = document.getElementById('editBtn');
        if (editBtn) {
            this.addEventListener(editBtn, 'click', () => this.handleEditResults());
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            this.addEventListener(refreshBtn, 'click', () => this.handleRefreshResults());
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            this.addEventListener(exportBtn, 'click', () => this.handleExportResults());
        }
    }

    /**
     * @function bindManualEditEvents - 绑定手动编辑事件
     * @description 绑定手动编辑相关按钮的事件
     */
    bindManualEditEvents() {
        // 添加订单按钮
        const addOrderBtn = document.getElementById('addOrderBtn');
        if (addOrderBtn) {
            this.addEventListener(addOrderBtn, 'click', () => this.handleAddOrder());
        }

        // 重新分析按钮
        const reAnalyzeBtn = document.getElementById('reAnalyzeBtn');
        if (reAnalyzeBtn) {
            this.addEventListener(reAnalyzeBtn, 'click', () => this.handleReAnalyze());
        }
    }

    /**
     * @function bindProfileEvents - 绑定Profile相关事件
     * @description 绑定Profile选择器和预览按钮的事件
     */
    bindProfileEvents() {
        // Profile选择器变化事件
        const profileSelect = document.getElementById('otaProfileSelect');
        if (profileSelect) {
            this.addEventListener(profileSelect, 'change', (e) => this.handleProfileChange(e));
        }

        // Profile预览按钮
        const profileInfoBtn = document.getElementById('profileInfoBtn');
        if (profileInfoBtn) {
            this.addEventListener(profileInfoBtn, 'click', () => this.showProfilePreview());
        }

        // Profile预览弹窗关闭按钮
        const profileModalClose = document.getElementById('profileModalClose');
        if (profileModalClose) {
            this.addEventListener(profileModalClose, 'click', () => this.hideProfilePreview());
        }

        // 点击弹窗背景关闭
        const profileModal = document.getElementById('profilePreviewModal');
        if (profileModal) {
            this.addEventListener(profileModal, 'click', (e) => {
                if (e.target === profileModal) {
                    this.hideProfilePreview();
                }
            });
        }
    }

    /**
     * @function bindUserSwitchEvent - 绑定用户切换事件
     * @description 监听用户切换的自定义事件
     */
    bindUserSwitchEvent() {
        window.addEventListener('userSwitch', (event) => {
            this.handleUserSwitch(event.detail);
        });
    }

    /**
     * @function addEventListener - 添加事件监听器
     * @description 安全地添加事件监听器并记录
     * @param {HTMLElement} element - 目标元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    addEventListener(element, event, handler) {
        if (!element) {
            logger.warn('InterfaceController', `元素不存在，跳过事件绑定: ${event}`);
            return;
        }

        const eventKey = `${element.id || 'unknown'}_${event}`;
        
        // 避免重复绑定
        if (this.boundEvents.has(eventKey)) {
            logger.debug('InterfaceController', `事件已绑定，跳过: ${eventKey}`);
            return;
        }

        element.addEventListener(event, handler);
        this.boundEvents.set(eventKey, { element, event, handler });
        this.eventListeners.set(eventKey, handler);
        
        logger.debug('InterfaceController', `事件绑定成功: ${eventKey}`);
    }

    // #endregion

    // #region 事件处理函数

    /**
     * @function handleLogin - 处理登录
     * @description 处理用户登录表单提交，支持登录信息持久化
     * @param {Event} event - 表单提交事件
     */
    async handleLogin(event) {
        event.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe')?.checked || false;

        if (!email || !password) {
            this.showError('请输入邮箱和密码');
            return;
        }

        logger.info('InterfaceController', '处理用户登录', { email, rememberMe });

        try {
            this.showLoading('正在登录...');

            // 直接调用API服务登录，支持记住我功能
            const result = await this.app.apiService.login(email, password, rememberMe);

            if (result.success) {
                this.hideLoginModal();
                this.updateConnectionStatus();
                this.updateSessionStatus(true, result.sessionSaved);

                // 显示登录成功消息
                const sessionMsg = result.sessionSaved
                    ? (rememberMe ? '（已保存7天）' : '（已保存24小时）')
                    : '';
                this.showSuccess(`登录成功${sessionMsg}`);

                // 加载系统数据
                await this.app.loadSystemData();
            } else {
                throw new Error(result.message || '登录失败');
            }

        } catch (error) {
            logger.error('InterfaceController', '登录失败', error);
            this.showError('登录失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * @function handleLogout - 处理登出
     * @description 处理用户登出请求，清除会话和持久化数据
     */
    async handleLogout() {
        try {
            logger.info('InterfaceController', '处理用户登出');

            this.showLoading('正在登出...');

            // 调用API服务登出
            const success = await this.app.apiService.logout();

            if (success) {
                // 更新UI状态
                this.updateSessionStatus(false);
                this.showLoginModal();
                this.showSuccess('已安全登出');

                // 清除界面数据
                this.clearUIData();

            } else {
                throw new Error('登出失败');
            }

        } catch (error) {
            logger.error('InterfaceController', '登出失败', error);
            this.showError('登出失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * @function clearUIData - 清除界面数据
     * @description 清除界面上的用户相关数据
     */
    clearUIData() {
        try {
            // 清除订单文本
            const orderTextArea = document.getElementById('orderText');
            if (orderTextArea) {
                orderTextArea.value = '';
            }

            // 清除结果显示
            const resultContainer = document.getElementById('resultContainer');
            if (resultContainer) {
                resultContainer.style.display = 'none';
            }

            // 重置选择器
            this.resetSelectors();

            logger.debug('InterfaceController', '界面数据已清除');

        } catch (error) {
            logger.error('InterfaceController', '清除界面数据失败', error);
        }
    }

    /**
     * @function resetSelectors - 重置选择器
     * @description 重置所有下拉选择器到默认状态
     */
    resetSelectors() {
        try {
            const selectors = [
                'carTypeSelect',
                'subCategorySelect',
                'backendUserSelect'
            ];

            selectors.forEach(selectorId => {
                const selector = document.getElementById(selectorId);
                if (selector) {
                    selector.selectedIndex = 0;
                }
            });

        } catch (error) {
            logger.error('InterfaceController', '重置选择器失败', error);
        }
    }

    /**
     * @function saveLastLoginInfo - 保存最后登录信息
     * @description 保存用户最后一次成功登录的邮箱信息
     * @param {string} email - 用户邮箱
     */
    saveLastLoginInfo(email) {
        try {
            const loginInfo = {
                email,
                timestamp: Date.now()
            };
            
            localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.LAST_LOGIN, JSON.stringify(loginInfo));
            
            logger.debug('InterfaceController', '保存登录信息', { 
                email,
                timestamp: new Date(loginInfo.timestamp).toLocaleString()
            });
        } catch (error) {
            logger.warn('InterfaceController', '保存登录信息失败', error);
        }
    }

    /**
     * @function handleLogout - 处理登出
     * @description 处理用户登出操作
     */
    handleLogout() {
        this.app.apiService.logout();
        this.showLoginModal();
        document.getElementById('mainApp').classList.add('hidden');
        this.showInfo('已退出登录');
    }

    /**
     * @function handleProcessOrder - 处理订单处理
     * @description 处理订单文本处理请求
     */
    async handleProcessOrder() {
        // 确保核心模块已加载
        if (!this.app.orderManager) {
            this.showError('订单管理模块尚未加载，请稍后重试');
            return;
        }

        try {
            await this.app.orderManager.handleProcessOrder();
        } catch (error) {
            this.showError('订单处理失败: ' + error.message);
        }
    }

    /**
     * @function handleCreateOrders - 处理创建订单
     * @description 处理批量创建订单请求
     */
    async handleCreateOrders() {
        if (!this.app.orderManager) {
            this.showError('订单管理模块尚未加载，请稍后重试');
            return;
        }

        try {
            await this.app.orderManager.handleCreateOrders();
        } catch (error) {
            this.showError('创建订单失败: ' + error.message);
        }
    }

    /**
     * @function handleFileUpload - 处理文件上传
     * @description 处理图片文件上传
     * @param {Event} event - 文件选择事件
     */
    async handleFileUpload(event) {
        // 确保图像处理模块已加载
        await this.app.ensureModuleLoaded('imageProcessing');
        
        const files = event.target.files;
        if (files.length > 0) {
            try {
                this.showLoading('正在处理图片...');
                
                // 确保ImageService已初始化
                if (!this.app.imageService) {
                    throw new Error('图像服务未初始化');
                }
                
                const result = await this.app.imageService.processImageFiles(files);
                
                if (result.success && result.results.length > 0) {
                    // 取第一个成功的结果，提取文字并填入文本框
                    const firstResult = result.results[0];
                    if (firstResult.ocrResult && firstResult.ocrResult.extractedText) {
                        const textArea = document.getElementById('orderText');
                        if (textArea) {
                            textArea.value = firstResult.ocrResult.extractedText;
                        }
                        this.showSuccess('图片文字提取成功');
                    }
                } else {
                    this.showError('图片处理未能提取到有效文字');
                }
                
            } catch (error) {
                this.showError('图片处理失败: ' + error.message);
            } finally {
                this.hideLoading();
            }
        }
    }

    /**
     * @function handleFileDrop - 处理文件拖拽
     * @description 处理拖拽文件上传
     * @param {Event} event - 拖拽事件
     */
    async handleFileDrop(event) {
        event.preventDefault();
        
        // 确保图像处理模块已加载
        await this.app.ensureModuleLoaded('imageProcessing');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            try {
                this.showLoading('正在处理拖拽的图片...');
                
                // 确保ImageService已初始化
                if (!this.app.imageService) {
                    throw new Error('图像服务未初始化');
                }
                
                const result = await this.app.imageService.processImageFiles(files);
                
                if (result.success && result.results.length > 0) {
                    // 取第一个成功的结果，提取文字并填入文本框
                    const firstResult = result.results[0];
                    if (firstResult.ocrResult && firstResult.ocrResult.extractedText) {
                        const textArea = document.getElementById('orderText');
                        if (textArea) {
                            textArea.value = firstResult.ocrResult.extractedText;
                        }
                        this.showSuccess('图片文字提取成功');
                    }
                } else {
                    this.showError('图片处理未能提取到有效文字');
                }
                
            } catch (error) {
                this.showError('图片处理失败: ' + error.message);
            } finally {
                this.hideLoading();
            }
        }
    }

    /**
     * @function handleDragOver - 处理拖拽悬停
     * @description 处理文件拖拽悬停效果
     * @param {Event} event - 拖拽悬停事件
     */
    handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.style.borderColor = '#007bff';
    }

    /**
     * @function handleUploadAreaClick - 处理上传区域点击
     * @description 处理上传区域点击，触发文件选择
     */
    handleUploadAreaClick() {
        const fileInput = document.getElementById('imageFile');
        if (fileInput) {
            fileInput.click();
        }
    }

    /**
     * @function handleLLMStatusClick - 处理LLM状态点击
     * @description 处理LLM状态指示器点击，检测连接
     * @param {string} llmType - LLM类型
     */
    async handleLLMStatusClick(llmType) {
        if (!this.app.llmService) {
            this.showError('LLM服务尚未加载');
            return;
        }

        try {
            this.showLoading(`正在检测${llmType}连接...`);
            await this.app.llmService.checkConnection(llmType);
        } catch (error) {
            this.showError(`${llmType}连接检测失败: ` + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * @function handleEditResults - 处理编辑结果
     * @description 切换到编辑模式
     */
    handleEditResults() {
        if (this.app.orderManager) {
            this.app.orderManager.handleEditResults();
        } else {
            this.showError('订单管理模块尚未加载');
        }
    }

    /**
     * @function handleRefreshResults - 处理刷新结果
     * @description 重新处理当前订单
     */
    async handleRefreshResults() {
        if (this.app.orderManager) {
            await this.app.orderManager.handleRefreshResults();
        } else {
            this.showError('订单管理模块尚未加载');
        }
    }

    /**
     * @function handleExportResults - 处理导出结果
     * @description 导出处理结果
     */
    handleExportResults() {
        try {
            this.exportOrderResults();
            this.showSuccess('结果导出成功');
        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    /**
     * @function handleAddOrder - 处理添加订单
     * @description 添加新的手动订单
     */
    handleAddOrder() {
        if (this.app.orderManager) {
            this.app.orderManager.handleAddOrder();
        } else {
            this.showError('订单管理模块尚未加载');
        }
    }

    /**
     * @function handleReAnalyze - 处理重新分析
     * @description 重新分析订单内容
     */
    async handleReAnalyze() {
        if (this.app.orderManager) {
            await this.app.orderManager.handleReAnalyze();
        } else {
            this.showError('订单管理模块尚未加载');
        }
    }

    /**
     * @function handleProfileChange - 处理Profile选择变化
     * @description 处理用户选择不同的Profile模板
     * @param {Event} event - 选择器变化事件
     */
    async handleProfileChange(event) {
        const profileId = event.target.value;
        
        try {
            // 确保OTA Profile管理器已加载
            if (!window.otaProfileManager) {
                await this.app.moduleLoader.loadOTAProfile();
            }
            
            if (window.otaProfileManager) {
                // 应用选择的Profile
                const success = window.otaProfileManager.applyProfile(profileId);
                
                if (success) {
                    // 更新UI状态
                    this.updateProfileSelector();
                    
                    // 显示成功消息
                    this.showSuccess(`已切换到 ${profileId} 配置模板`);
                    
                    logger.info('InterfaceController', 'Profile切换成功', { 
                        profileId: profileId 
                    });
                } else {
                    this.showError('Profile切换失败');
                    
                    // 恢复到之前的选择
                    const currentProfile = this.appState.currentProfile;
                    if (currentProfile) {
                        event.target.value = currentProfile.id;
                    }
                }
            } else {
                this.showError('Profile管理器未加载');
            }
            
        } catch (error) {
            logger.error('InterfaceController', 'Profile切换失败', error);
            this.showError('Profile切换失败: ' + error.message);
            
            // 恢复到之前的选择
            const currentProfile = this.appState.currentProfile;
            if (currentProfile) {
                event.target.value = currentProfile.id;
            }
        }
    }

    /**
     * @function handleUserSwitch - 处理用户切换
     * @description 处理用户切换事件
     * @param {object} detail - 用户切换详情
     */
    async handleUserSwitch(detail) {
        logger.info('InterfaceController', '用户切换', detail);
        // 实现用户切换逻辑
    }

    /**
     * @function exportOrderResults - 导出订单结果
     * @description 将订单结果导出为文件
     */
    exportOrderResults() {
        const results = this.appState.getProcessedOrders();
        if (!results || results.length === 0) {
            throw new Error('没有可导出的订单结果');
        }

        const dataStr = JSON.stringify(results, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `order_results_${new Date().toISOString().slice(0, 10)}.json`;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    // #endregion

    /**
     * @function updateUISelectors - 更新UI选择器
     * @description 更新所有下拉选择器的选项
     */
    updateUISelectors() {
        logger.info('InterfaceController', '更新UI选择器');

        // 更新核心选择器
        this.updateBackendUserSelector();
        this.updateSubCategorySelector();
        this.updateCarTypeSelector();
        
        // 更新扩展选择器
        this.updateDrivingRegionSelector();
        this.updateLanguagesSelector();
        
        // 更新Profile选择器
        this.updateProfileSelector();

        logger.debug('InterfaceController', 'UI选择器更新完成');
    }

    /**
     * @function updateBackendUserSelector - 更新后台用户选择器
     * @description 更新后台用户下拉选择器的选项
     */
    updateBackendUserSelector() {
        const selector = document.getElementById('backendUserSelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">选择后台用户...</option>';

        // 添加用户选项
        if (this.appState.backendUsers && this.appState.backendUsers.length > 0) {
            this.appState.backendUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.phone})`;
                selector.appendChild(option);
            });
            
            logger.debug('InterfaceController', '后台用户选择器更新完成', { 
                count: this.appState.backendUsers.length 
            });
        } else {
            logger.warn('InterfaceController', '没有可用的后台用户数据');
        }
    }

    /**
     * @function updateSubCategorySelector - 更新服务类型选择器
     * @description 更新服务类型下拉选择器的选项
     */
    updateSubCategorySelector() {
        const selector = document.getElementById('subCategorySelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">选择服务类型...</option>';

        // 添加服务类型选项
        if (this.appState.subCategories && this.appState.subCategories.length > 0) {
            this.appState.subCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                selector.appendChild(option);
            });
            
            logger.debug('InterfaceController', '服务类型选择器更新完成', { 
                count: this.appState.subCategories.length 
            });
        } else {
            logger.warn('InterfaceController', '没有可用的服务类型数据');
        }
    }

    /**
     * @function updateCarTypeSelector - 更新车型选择器
     * @description 更新车型下拉选择器的选项
     */
    updateCarTypeSelector() {
        const selector = document.getElementById('carTypeSelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">选择车型...</option>';

        // 添加车型选项
        if (this.appState.carTypes && this.appState.carTypes.length > 0) {
            this.appState.carTypes.forEach(carType => {
                const option = document.createElement('option');
                option.value = carType.id;
                option.textContent = `${carType.type} (${carType.seat_number}座)`;
                selector.appendChild(option);
            });
            
            logger.debug('InterfaceController', '车型选择器更新完成', { 
                count: this.appState.carTypes.length 
            });
        } else {
            logger.warn('InterfaceController', '没有可用的车型数据');
        }
    }

    /**
     * @function updateDrivingRegionSelector - 更新行驶区域选择器
     * @description 更新行驶区域下拉选择器的选项
     */
    updateDrivingRegionSelector() {
        const selector = document.getElementById('drivingRegionSelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">智能选择</option>';

        // 添加区域选项
        if (this.appState.drivingRegions && this.appState.drivingRegions.length > 0) {
            this.appState.drivingRegions.forEach(region => {
                const option = document.createElement('option');
                option.value = region.id;
                option.textContent = region.name;
                selector.appendChild(option);
            });
            
            logger.debug('InterfaceController', '行驶区域选择器更新完成', { 
                count: this.appState.drivingRegions.length 
            });
        } else {
            logger.debug('InterfaceController', '行驶区域数据未加载，使用降级模式');
        }
    }

    /**
     * @function updateLanguagesSelector - 更新语言选择器
     * @description 更新语言多选选择器的选项
     */
    updateLanguagesSelector() {
        const selector = document.getElementById('languagesSelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '';

        // 添加语言选项
        if (this.appState.languages && this.appState.languages.length > 0) {
            this.appState.languages.forEach(language => {
                const option = document.createElement('option');
                option.value = language.id;
                option.textContent = language.name;
                selector.appendChild(option);
            });
            
            logger.debug('InterfaceController', '语言选择器更新完成', { 
                count: this.appState.languages.length 
            });
        } else {
            logger.debug('InterfaceController', '语言数据未加载，使用降级模式');
        }
    }

    /**
     * @function updateProfileSelector - 更新Profile选择器
     * @description 更新OTA Profile选择器状态
     */
    updateProfileSelector() {
        const selector = document.getElementById('otaProfileSelect');
        const indicator = document.getElementById('currentProfileName');
        
        if (!selector || !indicator) return;

        // 获取当前Profile
        const currentProfile = this.appState.currentProfile;
        if (currentProfile) {
            // 更新选择器值
            selector.value = currentProfile.id;
            
            // 更新状态指示器
            indicator.textContent = currentProfile.name;
            
            logger.debug('InterfaceController', 'Profile选择器更新完成', { 
                profileId: currentProfile.id,
                profileName: currentProfile.name 
            });
        } else {
            // 默认状态
            selector.value = 'general';
            indicator.textContent = '通用模板';
            
            logger.debug('InterfaceController', 'Profile选择器设置为默认状态');
        }
    }

    /**
     * @function updateProfileDisplay - 更新Profile显示状态
     * @description 更新当前Profile的显示状态和UI元素
     */
    updateProfileDisplay() {
        try {
            // 获取当前Profile配置
            const currentProfile = this.appState.currentProfile;
            
            // 更新Profile名称显示
            const profileNameElement = document.getElementById('currentProfileName');
            if (profileNameElement) {
                profileNameElement.textContent = currentProfile ? currentProfile.name : '通用模板';
            }
            
            // 更新Profile选择器的选中状态
            const profileSelector = document.getElementById('otaProfileSelect');
            if (profileSelector && currentProfile) {
                profileSelector.value = currentProfile.id;
            }
            
            // 更新Profile指示器状态
            const profileIndicator = document.getElementById('profileIndicator');
            if (profileIndicator) {
                profileIndicator.className = 'profile-indicator';
                if (currentProfile && currentProfile.id !== 'general') {
                    profileIndicator.classList.add('profile-active');
                }
            }
            
            // 如果有Profile数据，应用到智能选择服务
            if (currentProfile && window.smartSelection) {
                window.smartSelection.currentProfileConfig = currentProfile;
                logger.info('InterfaceController', 'Profile显示状态已更新', {
                    profileName: currentProfile.name,
                    profileId: currentProfile.id
                });
            }
            
        } catch (error) {
            logger.error('InterfaceController', 'Profile显示更新失败', error);
        }
    }

    /**
     * @function clearUIDisplays - 清空UI显示内容
     * @description 清空所有UI显示区域的内容
     */
    clearUIDisplays() {
        // 清空选择器
        this.clearAllSelectors();

        // 隐藏结果区域
        const sections = ['resultPreview', 'orderStatus', 'manualEditSection'];
        sections.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.classList.add('hidden');
        });

        // 清空表单
        const forms = document.querySelectorAll('.order-edit-form');
        forms.forEach(form => form.remove());

        // 清空结果显示
        const resultsDiv = document.getElementById('orderResults');
        if (resultsDiv) resultsDiv.innerHTML = '';

        logger.debug('InterfaceController', 'UI显示内容清理完成');
    }

    /**
     * @function clearAllSelectors - 清空所有选择器
     * @description 重置所有下拉选择器为默认状态
     */
    clearAllSelectors() {
        const selectors = [
            'backendUserSelect', 
            'subCategorySelect', 
            'carTypeSelect',
            'drivingRegionSelect',
            'languagesSelect'
        ];
        selectors.forEach(id => {
            const selector = document.getElementById(id);
            if (selector) {
                if (id === 'languagesSelect') {
                    selector.innerHTML = '';
                } else if (id === 'drivingRegionSelect') {
                    selector.innerHTML = '<option value="">智能选择</option>';
                } else {
                    selector.innerHTML = '<option value="">选择...</option>';
                }
            }
        });
    }

    /**
     * @function showProfilePreview - 显示Profile配置预览
     * @description 显示当前Profile的详细配置信息
     */
    showProfilePreview() {
        const modal = document.getElementById('profilePreviewModal');
        const content = document.getElementById('profilePreviewContent');
        
        if (!modal || !content) return;

        // 获取当前Profile
        const currentProfile = this.appState.currentProfile || SYSTEM_CONFIG.OTA_PROFILES.GENERAL;
        
        // 生成Profile配置内容
        const profileHtml = this.generateProfileConfigHTML(currentProfile);
        content.innerHTML = profileHtml;
        
        // 显示弹窗
        modal.style.display = 'flex';
        
        logger.info('InterfaceController', '显示Profile配置预览', { 
            profileId: currentProfile.id 
        });
    }

    /**
     * @function hideProfilePreview - 隐藏Profile配置预览
     * @description 隐藏Profile配置预览弹窗
     */
    hideProfilePreview() {
        const modal = document.getElementById('profilePreviewModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * @function generateProfileConfigHTML - 生成Profile配置HTML
     * @description 生成Profile配置的详细HTML内容
     * @param {object} profile - Profile配置对象
     * @returns {string} HTML字符串
     */
    generateProfileConfigHTML(profile) {
        return `
            <div class="profile-config-section">
                <h4>基本信息</h4>
                <div class="profile-config-item">
                    <span class="profile-config-label">模板名称:</span>
                    <span class="profile-config-value">${profile.name}</span>
                </div>
                <div class="profile-config-item">
                    <span class="profile-config-label">模板ID:</span>
                    <span class="profile-config-value">${profile.id}</span>
                </div>
            </div>

            <div class="profile-config-section">
                <h4>默认设置</h4>
                <div class="profile-config-item">
                    <span class="profile-config-label">默认语言:</span>
                    <span class="profile-config-value">${this.formatLanguageIds(profile.defaultLanguages)}</span>
                </div>
                <div class="profile-config-item">
                    <span class="profile-config-label">默认区域:</span>
                    <span class="profile-config-value">${this.formatRegionId(profile.defaultRegion)}</span>
                </div>
                <div class="profile-config-item">
                    <span class="profile-config-label">默认用户:</span>
                    <span class="profile-config-value">${this.formatBackendUserId(profile.defaultBackendUser)}</span>
                </div>
            </div>

            <div class="profile-config-section">
                <h4>车型选择规则</h4>
                ${this.formatCarTypeRules(profile.defaultCarTypeRules)}
            </div>

            <div class="profile-config-section">
                <h4>服务类型偏好</h4>
                ${this.formatServicePreferences(profile.serviceTypePreferences)}
            </div>

            ${profile.specialRules ? `
            <div class="profile-config-section">
                <h4>特殊规则</h4>
                ${this.formatSpecialRules(profile.specialRules)}
            </div>
            ` : ''}
        `;
    }

    /**
     * @function formatLanguageIds - 格式化语言ID显示
     * @param {Array} languageIds - 语言ID数组
     * @returns {string} 格式化的语言名称
     */
    formatLanguageIds(languageIds) {
        if (!languageIds || !Array.isArray(languageIds)) return '未设置';
        
        const languageNames = languageIds.map(id => {
            const language = this.appState.languages?.find(lang => lang.id === id);
            return language ? language.name : `ID:${id}`;
        });
        
        return languageNames.join(', ');
    }

    /**
     * @function formatRegionId - 格式化区域ID显示
     * @param {number} regionId - 区域ID
     * @returns {string} 格式化的区域名称
     */
    formatRegionId(regionId) {
        if (!regionId) return '未设置';
        
        const region = this.appState.drivingRegions?.find(r => r.id === regionId);
        return region ? region.name : `ID:${regionId}`;
    }

    /**
     * @function formatBackendUserId - 格式化后台用户ID显示
     * @param {number} userId - 用户ID
     * @returns {string} 格式化的用户名称
     */
    formatBackendUserId(userId) {
        if (!userId) return '未设置';
        
        const user = this.appState.backendUsers?.find(u => u.id === userId);
        return user ? `${user.name} (${user.phone})` : `ID:${userId}`;
    }

    /**
     * @function formatCarTypeRules - 格式化车型规则显示
     * @param {object} rules - 车型规则对象
     * @returns {string} 格式化的规则HTML
     */
    formatCarTypeRules(rules) {
        if (!rules) return '<p>无特殊规则</p>';
        
        let html = '';
        for (const [condition, carTypeId] of Object.entries(rules)) {
            const carType = this.appState.carTypes?.find(ct => ct.id === carTypeId);
            const carTypeName = carType ? carType.type : `ID:${carTypeId}`;
            
            html += `
                <div class="profile-config-item">
                    <span class="profile-config-label">${condition}:</span>
                    <span class="profile-config-value">${carTypeName}</span>
                </div>
            `;
        }
        
        return html;
    }

    /**
     * @function formatServicePreferences - 格式化服务偏好显示
     * @param {object} preferences - 服务偏好对象
     * @returns {string} 格式化的偏好HTML
     */
    formatServicePreferences(preferences) {
        if (!preferences) return '<p>无特殊偏好</p>';
        
        let html = '';
        for (const [serviceType, subCategoryId] of Object.entries(preferences)) {
            const subCategory = this.appState.subCategories?.find(sc => sc.id === subCategoryId);
            const subCategoryName = subCategory ? subCategory.name : `ID:${subCategoryId}`;
            
            html += `
                <div class="profile-config-item">
                    <span class="profile-config-label">${serviceType}:</span>
                    <span class="profile-config-value">${subCategoryName}</span>
                </div>
            `;
        }
        
        return html;
    }

    /**
     * @function formatSpecialRules - 格式化特殊规则显示
     * @param {object} rules - 特殊规则对象
     * @returns {string} 格式化的规则HTML
     */
    formatSpecialRules(rules) {
        if (!rules) return '<p>无特殊规则</p>';
        
        let html = '';
        for (const [key, value] of Object.entries(rules)) {
            html += `
                <div class="profile-config-item">
                    <span class="profile-config-label">${key}:</span>
                    <span class="profile-config-value">${value}</span>
                </div>
            `;
        }
        
        return html;
    }

    /**
     * @function removeEventListeners - 移除事件监听器
     * @description 清理所有绑定的事件监听器
     */
    removeEventListeners() {
        for (const [eventKey, eventData] of this.boundEvents) {
            const { element, event, handler } = eventData;
            if (element && handler) {
                element.removeEventListener(event, handler);
            }
        }
        
        this.boundEvents.clear();
        this.eventListeners.clear();
        
        logger.info('InterfaceController', '所有事件监听器已移除');
    }

    /**
     * @function destroy - 销毁控制器
     * @description 清理资源并销毁控制器
     */
    destroy() {
        this.removeEventListeners();
        this.hideLoading();
        
        if (this.currentModal) {
            this.currentModal.style.display = 'none';
            this.currentModal = null;
        }
        
        this.clearObservers();
        
        logger.info('InterfaceController', '界面控制器已销毁');
    }
}

// 导出到全局作用域
window.InterfaceController = InterfaceController; 