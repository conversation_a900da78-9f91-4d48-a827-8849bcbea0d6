/**
 * @file app.js - OTA订单处理系统主应用（精简版）
 * @description 模块化重构后的主应用控制器，协调各个管理器模块
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-08
 * @version v4.2.0
 * @dependencies app-state.js, interface-controller.js, order-manager.js, resilience-manager.js
 */

/**
 * @class OTAOrderApp - OTA订单处理主应用类（精简版）
 * @description 协调各个模块，管理应用的整体流程和状态
 */
class OTAOrderApp {
    /**
     * @function constructor - 构造函数
     * @description 初始化主应用和所有管理器模块
     */
    constructor() {
        // 获取应用状态实例
        this.appState = window.appState;
        
        // 初始化基础服务（延迟初始化）
        this.apiService = new ApiService(this.appState);
        this.llmService = null;         // 延迟初始化
        this.orderParser = null;        // 延迟初始化
        this.imageService = null;       // 延迟初始化

        // 初始化会话管理器
        this.sessionManager = new SessionManager();
        this.apiService.setSessionManager(this.sessionManager);

        // 初始化管理器层（延迟初始化）
        this.interfaceController = new InterfaceController(this, this.appState);
        this.orderManager = null;       // 延迟初始化
        this.resilienceManager = null;  // 延迟初始化
        this.performanceOptimizer = null; // 延迟初始化
        
        // 应用状态
        this.isInitialized = false;
        
        logger.info('应用', 'OTAOrderApp实例创建完成', { version: 'v4.2.0' });
    }

    /**
     * @function initialize - 初始化应用
     * @description 协调各个模块的初始化流程，支持渐进式加载
     */
    async initialize() {
        if (this.isInitialized) {
            logger.debug('应用', '应用已初始化，跳过重复初始化');
            return;
        }

        logger.info('应用', '开始初始化OTA订单处理系统');

        try {
            // 阶段1：基础初始化（关键路径）
            await this.initializeCore();

            // 阶段2：等待核心模块加载
            this.waitForCoreModules();

            this.isInitialized = true;
            logger.success('应用', '系统基础初始化完成，等待模块加载');

        } catch (error) {
            logger.error('应用', '系统初始化失败', { error: error.message });
            this.interfaceController.showError('系统初始化失败: ' + error.message);
            throw error;
        }
    }

    /**
     * @function initializeCore - 初始化核心功能
     * @description 初始化关键路径上的核心功能
     */
    async initializeCore() {
        logger.info('应用', '初始化核心功能');

        // 1. 初始化UI组件和事件监听器
        this.interfaceController.initializeUI();
        this.interfaceController.bindEventListeners();
        this.interfaceController.bindUserSwitchEvent();

        // 2. 检查登录状态
        await this.checkAuthStatus();

        // 3. 显示加载进度
        this.interfaceController.showLoading('正在加载核心模块...');
    }

    /**
     * @function waitForCoreModules - 等待核心模块加载
     * @description 等待核心业务模块加载完成
     */
    waitForCoreModules() {
        // 设置模块加载回调
        this.onCoreModulesLoaded = async () => {
            logger.info('应用', '核心模块加载完成，继续初始化');
            await this.initializeWithCoreModules();
        };

        this.onAllModulesLoaded = async () => {
            logger.info('应用', '所有模块加载完成，启用完整功能');
            await this.initializeEnhancedFeatures();
        };
    }

    /**
     * @function initializeCoreServices - 初始化核心服务
     * @description 在核心模块加载后初始化依赖的服务
     */
    async initializeCoreServices() {
        logger.info('应用', '初始化核心服务模块');

        try {
            // 初始化LLM服务
            if (window.LLMService && !this.llmService) {
                this.llmService = new LLMService();
                // 将LLM服务注册到全局作用域，便于其他模块访问
                window.llmService = this.llmService;
                logger.success('应用', 'LLM服务初始化完成');
            } else if (!window.LLMService) {
                logger.warn('应用', 'LLMService类未加载，跳过初始化');
            }

            // 初始化订单解析器
            if (window.OrderParser && this.llmService && !this.orderParser) {
                this.orderParser = new OrderParser(this.llmService);
                // 将订单解析器注册到全局作用域
                window.orderParser = this.orderParser;
                logger.success('应用', '订单解析器初始化完成');
            } else if (!window.OrderParser) {
                logger.warn('应用', 'OrderParser类未加载，跳过初始化');
            }

            // 初始化弹性管理器
            if (window.ResilienceManager && !this.resilienceManager) {
                this.resilienceManager = new ResilienceManager(this.appState, this.apiService);
                // 将弹性管理器注册到全局作用域
                window.resilienceManager = this.resilienceManager;
                logger.success('应用', '弹性管理器初始化完成');
            } else if (!window.ResilienceManager) {
                logger.warn('应用', 'ResilienceManager类未加载，跳过初始化');
            }

            // 初始化图像服务（如果已加载）
            if (window.ImageService && !this.imageService) {
                this.imageService = new ImageService();
                // 将图像服务注册到全局作用域
                window.imageService = this.imageService;
                logger.success('应用', '图像服务初始化完成');
            }

        } catch (error) {
            logger.error('应用', '核心服务初始化失败', error);
            throw error;
        }
    }

    /**
     * @function initializeWithCoreModules - 使用核心模块初始化
     * @description 核心模块加载后的初始化
     */
    async initializeWithCoreModules() {
        try {
            logger.info('应用', '开始核心模块初始化');

            // 更新加载消息
            this.interfaceController.updateLoadingMessage('正在初始化核心服务...');

            // 初始化核心模块依赖的服务
            await this.initializeCoreServices();

            // 初始化智能选择服务
            if (window.SmartSelectionService && !window.smartSelection) {
                window.smartSelection = new SmartSelectionService();
                await window.smartSelection.initialize();
                logger.success('应用', '智能选择服务初始化完成');
            } else if (!window.SmartSelectionService) {
                logger.warn('应用', 'SmartSelectionService类未加载，跳过初始化');
            }

            // 重新创建依赖核心模块的管理器
            if (window.OrderManager && this.orderParser) {
                this.orderManager = new OrderManager(this.appState, this.orderParser, window.smartSelection);
                // 将OrderManager注册到全局作用域
                window.orderManager = this.orderManager;
                logger.success('应用', 'OrderManager初始化完成');
            } else {
                logger.warn('应用', 'OrderManager初始化失败', {
                    OrderManagerExists: !!window.OrderManager,
                    orderParserExists: !!this.orderParser,
                    smartSelectionExists: !!window.smartSelection
                });
            }

            // 初始化LLM连接检测（不需要认证）
            if (this.llmService) {
                logger.info('应用', '开始LLM状态检测');
                this.initializeLLMStatus();
            } else {
                logger.warn('应用', 'LLM服务未初始化，设置默认状态');
                // 设置默认的Gemini状态为未连接
                this.interfaceController.updateLLMStatusUI({
                    gemini: { status: 'disconnected', message: 'LLM服务未初始化' }
                });
            }

            // 检查用户登录状态，决定是否执行需要认证的操作
            if (this.appState.token && this.appState.userInfo) {
                logger.info('应用', '用户已登录，执行完整初始化');
                await this.initializeAuthenticatedFeatures();
            } else {
                logger.info('应用', '用户未登录，跳过需要认证的功能');
            }

            // 清除loading状态
            this.interfaceController.hideLoading();

            // 添加性能监控面板（开发模式）
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                this.interfaceController.addPerformanceMonitor();
            }

            // 验证关键功能是否可用
            this.validateCoreFeatures();

            logger.success('应用', '核心功能初始化完成');

        } catch (error) {
            logger.error('应用', '核心模块初始化失败', error);
            this.interfaceController.showError('核心模块初始化失败: ' + error.message);
            this.interfaceController.hideLoading();
        }
    }

    /**
     * @function validateCoreFeatures - 验证核心功能
     * @description 验证关键功能是否正确初始化
     */
    validateCoreFeatures() {
        const features = {
            'LLM服务': !!this.llmService,
            '订单解析器': !!this.orderParser,
            '订单管理器': !!this.orderManager,
            '接口控制器': !!this.interfaceController
        };

        logger.info('应用', '核心功能验证结果', features);

        // 检查是否有关键功能缺失
        const missingFeatures = Object.entries(features)
            .filter(([name, available]) => !available)
            .map(([name]) => name);

        if (missingFeatures.length > 0) {
            logger.warn('应用', '部分核心功能不可用', { missingFeatures });
            this.interfaceController.showWarning(`部分功能不可用: ${missingFeatures.join(', ')}`);
        }
    }

    /**
     * @function initializeAuthenticatedFeatures - 初始化需要认证的功能
     * @description 用户登录后执行的初始化操作
     */
    async initializeAuthenticatedFeatures() {
        try {
            // 加载系统数据
            await this.loadSystemData();

            // 启动数据验证调度
            if (this.resilienceManager) {
                this.resilienceManager.scheduleValidation();
            }

            // 初始化性能优化器
            await this.initializePerformanceOptimizer();

            // 启动定期内存优化
            if (this.performanceOptimizer && this.performanceOptimizer.scheduleMemoryOptimization) {
                this.performanceOptimizer.scheduleMemoryOptimization();
            } else {
                this.scheduleBasicMemoryOptimization();
            }

            logger.success('应用', '认证功能初始化完成');

        } catch (error) {
            logger.error('应用', '认证功能初始化失败', error);
            // 认证功能失败不影响基础功能
        }
    }

    /**
     * @function initializePerformanceOptimizer - 初始化性能优化器
     * @description 初始化性能监控和优化模块
     */
    async initializePerformanceOptimizer() {
        try {
            logger.info('应用', '初始化性能优化器');

            // 检查是否有性能优化器模块
            if (window.PerformanceOptimizer) {
                this.performanceOptimizer = new PerformanceOptimizer(this.appState, this.apiService);
                
                // 添加超时保护，防止初始化卡住
                const initPromise = this.performanceOptimizer.initialize();
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('性能优化器初始化超时')), 10000); // 10秒超时
                });
                
                await Promise.race([initPromise, timeoutPromise]);
                
                logger.success('应用', '性能优化器初始化完成');
            } else {
                logger.warn('应用', '性能优化器模块未加载，跳过初始化');
            }

        } catch (error) {
            logger.error('应用', '性能优化器初始化失败', error);
            // 性能优化器失败不影响核心功能
        }
    }

    /**
     * @function initializeEnhancedFeatures - 初始化增强功能
     * @description 所有模块加载后的增强功能初始化
     */
    async initializeEnhancedFeatures() {
        try {
            logger.info('应用', '初始化增强功能');

            // 初始化智能选择服务
            await this.initializeSmartSelection();

            // 初始化地址搜索服务
            await this.initializeAddressSearchService();

            // 初始化图像服务
            if (window.ImageService) {
                this.imageService = new ImageService();
                logger.success('应用', '图像处理服务已就绪');
            }

            logger.success('应用', '所有增强功能初始化完成');

        } catch (error) {
            logger.error('应用', '增强功能初始化失败', error);
            // 增强功能失败不影响核心功能
        }
    }

    /**
     * @function ensureModuleLoaded - 确保模块已加载
     * @description 按需加载特定模块
     * @param {string} moduleName - 模块名称
     */
    async ensureModuleLoaded(moduleName) {
        if (!window.moduleLoader) {
            logger.warn('应用', '模块加载器不可用');
            return false;
        }

        try {
            switch (moduleName) {
                case 'imageProcessing':
                    await window.moduleLoader.loadImageProcessing();
                    if (window.ImageService && !this.imageService) {
                        this.imageService = new ImageService();
                    }
                    break;
                case 'addressSearch':
                    await window.moduleLoader.loadAddressSearch();
                    this.initializeAddressSearchService();
                    break;
                case 'smartSelection':
                    await window.moduleLoader.loadSmartSelection();
                    this.initializeSmartSelection();
                    break;
                case 'otaProfile':
                    await window.moduleLoader.loadOTAProfile();
                    logger.success('应用', 'OTA Profile模块加载完成');
                    break;
                default:
                    logger.warn('应用', `未知的模块名称: ${moduleName}`);
                    return false;
            }
            return true;
        } catch (error) {
            logger.error('应用', `模块${moduleName}加载失败`, error);
            return false;
        }
    }

    /**
     * @function checkAuthStatus - 检查认证状态
     * @description 检查用户登录状态并更新UI，支持本地模式跳过登录和会话恢复
     */
    async checkAuthStatus() {
        logger.info('应用', '检查认证状态');

        // 检查是否启用本地模式
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('应用', '本地模式已启用，跳过登录验证');

            try {
                // 填充本地数据到应用状态
                window.localDataProvider.populateAppState(this.appState);

                // 隐藏登录界面
                this.interfaceController.hideLoginModal();
                this.interfaceController.updateConnectionStatus();

                // 显示本地模式通知
                const localModeNotice = document.getElementById('localModeNotice');
                if (localModeNotice) {
                    localModeNotice.style.display = 'block';
                }

                // 如果有增强登录处理器，通知其本地模式状态
                if (window.enhancedLoginHandler) {
                    window.enhancedLoginHandler.showLocalModeNotification();
                }

                logger.success('应用', '本地模式初始化完成');
                return;

            } catch (error) {
                logger.error('应用', '本地模式初始化失败', error);
                // 继续正常登录流程
            }
        }

        // 尝试从持久化存储恢复会话
        try {
            const sessionRestored = await this.apiService.restoreSession();
            if (sessionRestored) {
                logger.success('应用', '会话恢复成功');
                this.interfaceController.hideLoginModal();
                this.interfaceController.updateConnectionStatus();
                this.interfaceController.updateSessionStatus(true);
                return;
            }
        } catch (error) {
            logger.warn('应用', '会话恢复失败', error);
        }

        // 检查当前应用状态中的登录信息
        if (this.appState.token && this.appState.userInfo) {
            try {
                // 验证token有效性
                const isValid = await this.apiService.validateToken();
                if (isValid) {
                    this.interfaceController.hideLoginModal();
                    this.interfaceController.updateConnectionStatus();
                    this.interfaceController.updateSessionStatus(true);
                    logger.success('应用', '用户已登录', {
                        user: this.appState.userInfo.email
                    });
                } else {
                    throw new Error('Token验证失败');
                }
            } catch (error) {
                logger.warn('应用', 'Token验证失败，需要重新登录', error);
                this.appState.clearAuth();
                await this.sessionManager.clearSession();
                this.interfaceController.showLoginModal();
                this.interfaceController.updateSessionStatus(false);
            }
        } else {
            logger.debug('应用', '用户未登录');
            this.interfaceController.showLoginModal();
            this.interfaceController.updateSessionStatus(false);

            // 预填充最后登录的邮箱
            const lastEmail = this.sessionManager.getLastLoginEmail();
            if (lastEmail) {
                this.interfaceController.prefillLoginEmail(lastEmail);
            }
        }
    }

    /**
     * @function initializeLLMStatus - 初始化LLM状态
     * @description 检查LLM服务连接状态
     */
    initializeLLMStatus() {
        logger.info('应用', '初始化LLM状态检查');

        // 立即更新UI为检测中状态
        this.interfaceController.updateLLMStatusUI({
            gemini: { status: 'checking', message: '正在检测连接...' }
        });

        // 异步检查LLM连接状态
        this.checkLLMConnections().catch(error => {
            logger.warn('应用', 'LLM连接检查失败', error);
            // 更新UI为失败状态
            this.interfaceController.updateLLMStatusUI({
                gemini: { status: 'disconnected', message: '连接失败: ' + error.message }
            });
        });
    }

    /**
     * @function checkLLMConnections - 检查LLM连接
     * @description 检查所有LLM服务的连接状态
     */
    async checkLLMConnections() {
        try {
            logger.info('应用', '开始检查Gemini连接状态');

            // 确保LLM服务已初始化
            if (!this.llmService) {
                throw new Error('LLM服务未初始化');
            }

            // 检查Gemini连接
            const geminiResult = await this.llmService.checkGeminiConnection();

            // 处理返回结果（可能是布尔值或对象）
            let geminiStatus;
            if (typeof geminiResult === 'boolean') {
                geminiStatus = {
                    status: geminiResult ? 'connected' : 'disconnected',
                    message: geminiResult ? '连接正常' : '连接失败'
                };
            } else if (geminiResult && typeof geminiResult === 'object') {
                // 如果返回的是状态对象，直接使用
                geminiStatus = geminiResult;
            } else {
                // 默认处理
                geminiStatus = {
                    status: 'disconnected',
                    message: '未知状态'
                };
            }

            // 更新UI状态
            if (this.interfaceController) {
                this.interfaceController.updateLLMStatusUI({
                    gemini: geminiStatus
                });
            }

            logger.info('应用', 'LLM连接状态检查完成', {
                geminiResult,
                geminiStatus
            });

        } catch (error) {
            logger.error('应用', 'LLM连接检查失败', error);

            // 更新UI为错误状态
            if (this.interfaceController) {
                this.interfaceController.updateLLMStatusUI({
                    gemini: {
                        status: 'disconnected',
                        message: '检测失败: ' + error.message
                    }
                });
            }
        }
    }

    /**
     * @function handleTokenExpired - 处理token过期
     * @description 当token过期时的处理逻辑
     */
    handleTokenExpired() {
        logger.warn('应用', 'Token已过期，需要重新登录');

        // 清除应用状态
        this.appState.clearAuth();

        // 显示登录界面
        this.interfaceController.showLoginModal();
        this.interfaceController.updateSessionStatus(false);

        // 显示过期提示
        this.interfaceController.showWarning('登录已过期，请重新登录');
    }

    /**
     * @function handleSessionCleared - 处理会话被清除
     * @description 当其他标签页清除会话时的处理逻辑
     */
    handleSessionCleared() {
        logger.info('应用', '检测到会话被清除，同步状态');

        // 清除应用状态
        this.appState.clearAuth();

        // 更新UI状态
        this.interfaceController.updateSessionStatus(false);
        this.interfaceController.showLoginModal();
    }

    /**
     * @function handleSessionCreated - 处理会话被创建
     * @description 当其他标签页创建会话时的处理逻辑
     */
    async handleSessionCreated() {
        logger.info('应用', '检测到新会话创建，尝试同步');

        try {
            // 尝试恢复会话
            const sessionRestored = await this.apiService.restoreSession();
            if (sessionRestored) {
                this.interfaceController.hideLoginModal();
                this.interfaceController.updateConnectionStatus();
                this.interfaceController.updateSessionStatus(true);
                this.interfaceController.showSuccess('已同步其他标签页的登录状态');
            }
        } catch (error) {
            logger.error('应用', '同步会话失败', error);
        }
    }

    /**
     * @function loadSystemData - 加载系统数据
     * @description 加载所有必需的系统数据，支持分层加载、增量更新和性能优化，支持本地模式
     */
    async loadSystemData() {
        // 检查本地模式
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('应用', '本地模式已启用，使用本地数据');
            try {
                window.localDataProvider.populateAppState(this.appState);
                this.interfaceController.updateUISelectors();
                logger.success('应用', '本地数据加载完成');
                return;
            } catch (error) {
                logger.error('应用', '本地数据加载失败', error);
                this.interfaceController.showError('本地数据加载失败: ' + error.message);
                return;
            }
        }

        if (!this.appState.token) {
            logger.debug('应用', '未登录，跳过系统数据加载');
            return;
        }

        const startTime = Date.now();
        logger.info('应用', '开始加载系统数据');

        try {
            // 添加整体超时保护
            const loadDataPromise = this.loadSystemDataInternal();
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('系统数据加载超时')), 30000); // 30秒超时
            });
            
            await Promise.race([loadDataPromise, timeoutPromise]);

        } catch (error) {
            logger.error('应用', '系统数据加载失败', error);
            this.interfaceController.showError('系统数据加载失败: ' + error.message);
            
            // 尝试降级处理
            await this.handleDataLoadingFailure(error);
        } finally {
            this.cleanupLoadingProgress();
        }
    }

    /**
     * @function loadSystemDataInternal - 内部系统数据加载逻辑
     * @description 实际的数据加载逻辑，被超时保护包装
     */
    async loadSystemDataInternal() {
        const startTime = Date.now();
        
        // 检查缓存状态和增量更新需求
        const needsFullUpdate = await this.checkDataCacheStatus();
        
        if (!needsFullUpdate) {
            logger.info('应用', '缓存数据有效，跳过重新加载');
            return;
        }

        // 初始化加载进度
        this.initializeLoadingProgress();
        
        // 第一优先级：核心数据（阻塞加载）
        await this.loadCoreDataWithProgress();
        
        // 第二优先级：扩展数据（后台加载）
        await this.loadEnhancedDataWithProgress();

        // 完成加载统计
        const loadTime = Date.now() - startTime;
        this.updateLoadingStats(loadTime);
        
        // 检查是否需要使用测试数据
        if (window.testDataProvider) {
            const testModeEnabled = window.testDataProvider.checkAndEnableTestModeIfNeeded(this.appState);
            if (testModeEnabled) {
                window.testDataProvider.populateAppStateWithTestData(this.appState);
                // 重新更新UI选择器
                this.interfaceController.updateUISelectors();
            }
        }
        
        logger.success('应用', '系统数据加载完成', { 
            loadTime: `${loadTime}ms`,
            cacheHit: !needsFullUpdate,
            testMode: window.testDataProvider?.isTestMode || false
        });
    }

    /**
     * @function checkDataCacheStatus - 检查数据缓存状态
     * @description 检查缓存数据是否需要更新，实现增量更新策略
     * @returns {Promise<boolean>} 是否需要完整更新
     */
    async checkDataCacheStatus() {
        try {
            // 检查数据完整性
            const validation = this.appState.validateDataIntegrity();
            if (!validation.isValid) {
                logger.info('应用', '缓存数据无效，需要完整更新');
                return true;
            }

            // 检查缓存时间（如果超过1小时则更新）
            const lastUpdate = this.appState.getSystemDataTimestamp();
            const cacheAge = Date.now() - lastUpdate;
            const CACHE_TTL = 60 * 60 * 1000; // 1小时

            if (cacheAge > CACHE_TTL) {
                logger.info('应用', '缓存数据过期，需要更新', { 
                    cacheAge: `${Math.round(cacheAge / 1000 / 60)}分钟` 
                });
                return true;
            }

            // 检查强制刷新标志
            if (this.appState.getForceRefreshFlag()) {
                logger.info('应用', '检测到强制刷新标志');
                this.appState.clearForceRefreshFlag();
                return true;
            }

            return false;

        } catch (error) {
            logger.warn('应用', '缓存状态检查失败，使用完整更新', error);
            return true;
        }
    }

    /**
     * @function initializeLoadingProgress - 初始化加载进度
     * @description 初始化进度指示器和统计收集
     */
    initializeLoadingProgress() {
        this.loadingProgress = {
            total: 5,           // 总共5个API
            completed: 0,       // 已完成数量
            failed: 0,          // 失败数量
            startTime: Date.now(),
            stages: {
                core: { total: 3, completed: 0 },      // 核心数据：3个API
                enhanced: { total: 2, completed: 0 }    // 增强数据：2个API
            }
        };

        // 显示带进度条的加载指示器
        if (this.interfaceController && this.interfaceController.showLoadingWithProgress) {
            this.interfaceController.showLoadingWithProgress('准备加载系统数据...', true);
        }

        // 显示初始进度
        this.updateProgressDisplay();
    }

    /**
     * @function loadCoreDataWithProgress - 加载核心数据并更新进度
     * @description 加载核心数据（用户、服务类型、车型），支持进度更新
     */
    async loadCoreDataWithProgress() {
        this.interfaceController.showLoading('加载核心数据...');
        
        const coreApis = [
            { name: 'backendUsers', method: () => this.apiService.getBackendUsers() },
            { name: 'subCategories', method: () => this.apiService.getSubCategories() },
            { name: 'carTypes', method: () => this.apiService.getCarTypes() }
        ];

        for (const api of coreApis) {
            try {
                await api.method();
                this.loadingProgress.stages.core.completed++;
                this.loadingProgress.completed++;
                
                logger.debug('应用', `${api.name}数据加载完成`);
                this.updateProgressDisplay();
                
            } catch (error) {
                this.loadingProgress.failed++;
                logger.error('应用', `${api.name}数据加载失败`, error);
                throw error; // 核心数据失败则终止加载
            }
        }

        // 更新UI选择器（核心数据）
        this.interfaceController.updateUISelectors();
        logger.success('应用', '核心数据加载完成');
    }

    /**
     * @function loadEnhancedDataWithProgress - 加载增强数据并更新进度
     * @description 加载增强数据（区域、语言），支持部分失败处理
     */
    async loadEnhancedDataWithProgress() {
        this.interfaceController.showLoading('加载增强数据...');
        
        const enhancedApis = [
            { name: 'drivingRegions', method: () => this.apiService.getDrivingRegions() },
            { name: 'languages', method: () => this.apiService.getLanguages() }
        ];

        // 使用Promise.allSettled支持部分失败
        const results = await Promise.allSettled(
            enhancedApis.map(api => api.method())
        );

        // 处理结果并更新进度
        results.forEach((result, index) => {
            const apiName = enhancedApis[index].name;
            
            if (result.status === 'fulfilled') {
                this.loadingProgress.stages.enhanced.completed++;
                this.loadingProgress.completed++;
                logger.debug('应用', `${apiName}数据加载完成`);
            } else {
                this.loadingProgress.failed++;
                logger.warn('应用', `${apiName}数据加载失败`, result.reason);
            }
            
            this.updateProgressDisplay();
        });

        // 更新UI选择器（包含增强数据）
        this.interfaceController.updateUISelectors();
        
        // 初始化增强功能
        if (this.loadingProgress.stages.enhanced.completed > 0) {
            this.initializeEnhancedFeatures();
        }
        
        logger.success('应用', '增强数据加载完成', {
            successful: this.loadingProgress.stages.enhanced.completed,
            failed: this.loadingProgress.failed
        });
    }

    /**
     * @function updateProgressDisplay - 更新进度显示
     * @description 更新UI中的加载进度指示器
     */
    updateProgressDisplay() {
        const progress = this.loadingProgress;
        const percentage = Math.round((progress.completed / progress.total) * 100);
        const elapsed = Date.now() - progress.startTime;
        
        // 使用InterfaceController的进度更新方法
        if (this.interfaceController && this.interfaceController.updateLoadingProgress) {
            const progressText = `${progress.completed}/${progress.total} (${percentage}%)`;
            const performanceInfo = `用时: ${Math.round(elapsed / 1000)}s`;
            
            this.interfaceController.updateLoadingProgress(
                percentage,
                progressText,
                performanceInfo
            );
        }

        // 更新基本加载信息
        this.interfaceController.showLoading(
            `加载中 ${progress.completed}/${progress.total} (${percentage}%) - ${Math.round(elapsed / 1000)}s`
        );

        logger.debug('应用', '加载进度更新', {
            percentage,
            completed: progress.completed,
            total: progress.total,
            elapsed: `${elapsed}ms`
        });
    }

    /**
     * @function updateLoadingStats - 更新加载统计
     * @description 收集和更新加载性能统计数据
     * @param {number} totalTime - 总加载时间（毫秒）
     */
    updateLoadingStats(totalTime) {
        const stats = {
            totalTime,
            apiCount: this.loadingProgress.total,
            successCount: this.loadingProgress.completed,
            failureCount: this.loadingProgress.failed,
            successRate: (this.loadingProgress.completed / this.loadingProgress.total) * 100,
            averageApiTime: totalTime / this.loadingProgress.total,
            timestamp: Date.now()
        };

        // 保存到应用状态用于性能监控
        this.appState.updateLoadingStats(stats);

        // 记录性能日志
        logger.info('应用', '数据加载性能统计', stats);

        // 如果性能较差，记录警告
        if (totalTime > 10000) { // 超过10秒
            logger.warn('应用', '数据加载性能较差', {
                totalTime: `${totalTime}ms`,
                recommendation: '考虑优化网络连接或增加缓存策略'
            });
        }
    }

    /**
     * @function cleanupLoadingProgress - 清理加载进度
     * @description 清理进度指示器和相关资源
     */
    cleanupLoadingProgress() {
        this.interfaceController.hideLoading();
        
        // 清理进度相关的DOM元素
        const progressElements = document.querySelectorAll('.loading-progress-bar, .loading-progress-text');
        progressElements.forEach(element => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });

        // 清理进度状态
        this.loadingProgress = null;
    }

    /**
     * @function handleDataLoadingFailure - 处理数据加载失败
     * @description 数据加载失败时的降级处理
     * @param {Error} error - 错误对象
     */
    async handleDataLoadingFailure(error) {
        logger.warn('应用', '开始数据加载降级处理', { error: error.message });
        
        try {
            // 检查是否有缓存数据可用
            const validation = this.appState.validateDataIntegrity();
            
            if (!validation.isValid) {
                // 尝试从缓存恢复
                this.appState.loadUserSystemData();
                
                const retryValidation = this.appState.validateDataIntegrity();
                if (retryValidation.isValid) {
                    logger.success('应用', '从缓存恢复数据成功');
                    this.interfaceController.updateUISelectors();
                    return;
                }
            }
            
            // 如果缓存也无效，显示错误提示
            this.interfaceController.showError('无法加载系统数据，请检查网络连接后重试');
            
        } catch (fallbackError) {
            logger.error('应用', '降级处理失败', fallbackError);
        }
    }

    /**
     * @function initializeSmartSelection - 初始化智能选择服务
     * @description 初始化智能选择服务并设置五维数据（支持按需加载）
     */
    async initializeSmartSelection() {
        try {
            // 如果智能选择服务未加载，尝试按需加载
            if (!window.smartSelection) {
                const smartSelectionLoaded = await this.ensureModuleLoaded('smartSelection');
                if (!smartSelectionLoaded) {
                    logger.warn('应用', '智能选择模块加载失败');
                    return;
                }
            }

            if (window.smartSelection) {
                // 更新五维系统数据
                window.smartSelection.updateSystemData(
                    this.appState.backendUsers,
                    this.appState.subCategories,
                    this.appState.carTypes,
                    this.appState.drivingRegions || [], // 新增：行驶区域数据
                    this.appState.languages || []       // 新增：语言数据
                );
                
                // 如果有当前Profile，应用Profile设置
                if (this.appState.currentProfile) {
                    window.smartSelection.applyProfile(this.appState.currentProfile);
                    logger.info('应用', '已应用OTA Profile配置', { 
                        profileName: this.appState.currentProfile.name 
                    });
                }
                
                logger.success('应用', '智能选择服务初始化完成', {
                    dimensions: 5,
                    hasRegions: (this.appState.drivingRegions || []).length > 0,
                    hasLanguages: (this.appState.languages || []).length > 0,
                    hasProfile: !!this.appState.currentProfile
                });
            }
        } catch (error) {
            logger.error('应用', '智能选择服务初始化失败', error);
        }
    }

    /**
     * @function initializeAddressSearchService - 初始化地址搜索服务
     * @description 初始化地址搜索服务（支持按需加载）
     */
    async initializeAddressSearchService() {
        try {
            // 如果地址搜索服务未加载，尝试按需加载
            if (!window.addressSearchService) {
                const addressSearchLoaded = await this.ensureModuleLoaded('addressSearch');
                if (!addressSearchLoaded) {
                    logger.warn('应用', '地址搜索模块加载失败');
                    return;
                }
            }

            if (window.addressSearchService) {
                // 地址搜索服务已在加载时自动初始化，检查状态
                const status = window.addressSearchService.getStatus();
                logger.success('应用', '地址搜索服务初始化完成', status);
            }
        } catch (error) {
            logger.error('应用', '地址搜索服务初始化失败', error);
        }
    }

    /**
     * @function handleLogin - 处理用户登录
     * @description 处理用户登录表单提交（Observer模式简化版）
     * @param {Event} event - 表单提交事件
     */
    async handleLogin(event) {
        event.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email || !password) {
            this.interfaceController.showError('请输入邮箱和密码');
            return;
        }

        logger.info('应用', '处理用户登录', { email });

        try {
            this.interfaceController.showLoading('登录中...');

            const result = await this.apiService.login(email, password);
            
            if (result.success) {
                // 设置认证信息（Observer模式会自动处理UI更新）
                this.appState.setToken(result.token);
                this.appState.setUserInfo(result.user);
                
                // 执行需要认证的功能初始化
                this.interfaceController.showLoading('初始化用户功能...');
                await this.initializeAuthenticatedFeatures();
                
                // 加载OTA Profile管理器
                await this.ensureModuleLoaded('otaProfile');
                
                // 自动应用用户配置
                if (window.otaProfileManager) {
                    await window.otaProfileManager.autoApplyProfileByEmail(result.user.email);
                }
                
                // 初始化智能选择服务
                await this.initializeSmartSelection();
                
                logger.success('应用', '用户登录成功', { user: result.user.email });
                
            } else {
                throw new Error(result.error || '登录失败');
            }

        } catch (error) {
            logger.error('应用', '登录失败', error);
            this.interfaceController.showError('登录失败: ' + error.message);
        } finally {
            this.interfaceController.hideLoading();
        }
    }

    /**
     * @function handleLogout - 处理用户登出
     * @description 处理用户登出操作（Observer模式简化版）
     */
    handleLogout() {
        logger.info('应用', '处理用户登出');
        
        try {
            // 重置智能选择服务
            if (window.smartSelection) {
                window.smartSelection.resetToDefaults();
            }
            
            // 清除认证信息（Observer模式会自动处理UI更新）
            this.appState.clearAuth();
            
            logger.success('应用', '用户登出完成');
            
        } catch (error) {
            logger.error('应用', '登出处理失败', error);
            this.interfaceController.showError('登出失败: ' + error.message);
        }
    }

    /**
     * @function handleUserSwitch - 处理用户切换
     * @description 处理用户账号切换事件（Observer模式简化版）
     * @param {object} detail - 切换详情
     */
    async handleUserSwitch(detail) {
        logger.info('应用', '处理用户切换事件', detail);
        
        try {
            // 重置智能选择服务
            if (window.smartSelection) {
                window.smartSelection.resetToDefaults();
            }
            
            // 显示切换提示
            const confirmed = await this.showUserSwitchConfirmation(detail);
            if (confirmed) {
                // 重新加载系统数据（Observer模式会自动更新UI）
                await this.loadSystemData();
                
                // 重新初始化智能选择服务
                await this.initializeSmartSelection();
                
                logger.success('应用', '用户切换处理完成');
            }
        } catch (error) {
            logger.error('应用', '用户切换处理失败', error);
            this.interfaceController.showError('用户切换失败: ' + error.message);
        }
    }

    /**
     * @function showUserSwitchConfirmation - 显示用户切换确认
     * @description 显示用户切换确认对话框
     * @param {object} switchInfo - 切换信息
     * @returns {Promise<boolean>} 用户确认结果
     */
    async showUserSwitchConfirmation(switchInfo) {
        return new Promise((resolve) => {
            const message = `检测到账号切换：\n从 ${switchInfo.oldUser} 切换到 ${switchInfo.newUser}\n\n系统将清理旧用户数据并重新加载。是否继续？`;
            
            if (confirm(message)) {
                resolve(true);
            } else {
                resolve(false);
            }
        });
    }

    /**
     * @function processUploadedFiles - 处理上传的文件
     * @description 处理用户上传的图片文件（按需加载图像处理模块）
     * @param {FileList} files - 文件列表
     */
    async processUploadedFiles(files) {
        if (!files || files.length === 0) return;

        logger.info('应用', '处理上传文件', { fileCount: files.length });

        try {
            this.interfaceController.showLoading('处理图片文件...');

            // 确保图像处理模块已加载
            const imageModuleLoaded = await this.ensureModuleLoaded('imageProcessing');
            if (!imageModuleLoaded) {
                throw new Error('图像处理模块加载失败');
            }

            // 确保图像服务已初始化
            if (!this.imageService && window.ImageService) {
                this.imageService = new ImageService();
            }

            if (!this.imageService) {
                throw new Error('图像服务未初始化');
            }

            for (const file of files) {
                if (file.type.startsWith('image/')) {
                    const extractedText = await this.imageService.extractTextFromImage(file);
                    if (extractedText) {
                        // 将提取的文本填入订单文本框
                        const textArea = document.getElementById('orderText');
                        if (textArea) {
                            textArea.value = extractedText;
                        }
                        
                        this.interfaceController.showSuccess('图片文字提取成功');
                        logger.success('应用', '图片文字提取完成', { 
                            textLength: extractedText.length 
                        });
                        break; // 只处理第一个图片文件
                    }
                }
            }

        } catch (error) {
            logger.error('应用', '文件处理失败', error);
            this.interfaceController.showError('文件处理失败: ' + error.message);
        } finally {
            this.interfaceController.hideLoading();
        }
    }

    /**
     * @function showError - 显示错误消息
     * @description 显示错误消息的便捷方法
     * @param {string} message - 错误消息
     */
    showError(message) {
        this.interfaceController.showError(message);
    }

    /**
     * @function showSuccess - 显示成功消息
     * @description 显示成功消息的便捷方法
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        this.interfaceController.showSuccess(message);
    }

    /**
     * @function scheduleBasicMemoryOptimization - 调度基础内存优化
     * @description 启动基础的内存优化调度机制
     */
    scheduleBasicMemoryOptimization() {
        logger.info('应用', '启动基础内存优化调度');
        
        // 每30分钟执行一次基础内存清理
        setInterval(() => {
            try {
                // 清理应用状态中的过期数据
                this.appState.optimizeMemoryUsage();
                
                // 如果有垃圾回收，尝试触发
                if (window.gc && typeof window.gc === 'function') {
                    window.gc();
                }
                
                logger.debug('应用', '基础内存优化完成');
            } catch (error) {
                logger.warn('应用', '基础内存优化失败', error);
            }
        }, 30 * 60 * 1000); // 30分钟
    }
}

// 创建全局实例
window.app = new OTAOrderApp();

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await window.app.initialize();
        logger.success('应用', 'OTA订单处理系统启动完成');
    } catch (error) {
        logger.error('应用', '系统启动失败', error);
    }
});

logger.info('模块', 'OTAOrderApp主应用模块加载完成', { version: 'v4.2.0' });
