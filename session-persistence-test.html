<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话持久化功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }
        
        .status-success {
            background: #00b894;
            color: white;
        }
        
        .status-error {
            background: #e17055;
            color: white;
        }
        
        .status-info {
            background: #74b9ff;
            color: white;
        }
        
        .test-button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #5f3dc4;
            transform: translateY(-2px);
        }
        
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .log-area {
            background: #2d3436;
            color: #ddd;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .success { color: #00b894; }
        .error { color: #e17055; }
        .warning { color: #fdcb6e; }
        .info { color: #74b9ff; }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .checkbox-group {
            margin: 15px 0;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: #555;
        }
        
        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 会话持久化功能测试</h1>
        
        <!-- 会话状态显示 -->
        <div class="test-section">
            <h2>1. 当前会话状态</h2>
            <p>检查当前的会话状态和持久化数据</p>
            
            <div>
                <span>会话状态: </span>
                <span id="sessionStatus" class="status-indicator status-info">检查中...</span>
            </div>
            
            <div>
                <span>持久化状态: </span>
                <span id="persistenceStatus" class="status-indicator status-info">检查中...</span>
            </div>
            
            <button class="test-button" onclick="checkSessionStatus()">检查会话状态</button>
            <button class="test-button" onclick="clearAllSessions()">清除所有会话</button>
        </div>
        
        <!-- 模拟登录测试 -->
        <div class="test-section">
            <h2>2. 模拟登录测试</h2>
            <p>测试会话保存和恢复功能</p>
            
            <div class="form-group">
                <label for="testEmail">测试邮箱:</label>
                <input type="email" id="testEmail" value="<EMAIL>" placeholder="输入测试邮箱">
            </div>
            
            <div class="checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="testRememberMe">
                    记住我（7天内自动登录）
                </label>
            </div>
            
            <button class="test-button" onclick="simulateLogin()">模拟登录</button>
            <button class="test-button" onclick="simulateLogout()">模拟登出</button>
        </div>
        
        <!-- 会话恢复测试 -->
        <div class="test-section">
            <h2>3. 会话恢复测试</h2>
            <p>测试页面刷新后的会话恢复功能</p>
            
            <button class="test-button" onclick="testSessionRestore()">测试会话恢复</button>
            <button class="test-button" onclick="refreshPage()">刷新页面测试</button>
        </div>
        
        <!-- 多标签页同步测试 -->
        <div class="test-section">
            <h2>4. 多标签页同步测试</h2>
            <p>测试多个标签页间的会话状态同步</p>
            
            <button class="test-button" onclick="openNewTab()">打开新标签页</button>
            <button class="test-button" onclick="testCrossTabSync()">测试标签页同步</button>
        </div>
        
        <!-- 测试日志 -->
        <div class="log-area" id="testLog">
            <div class="info">🔧 会话持久化测试工具已就绪</div>
            <div class="info">📋 请点击上方按钮开始测试</div>
        </div>
    </div>

    <script>
        // 日志记录函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 更新状态指示器
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator status-${status}`;
            element.textContent = text;
        }

        // 检查会话状态
        function checkSessionStatus() {
            log('🔍 检查当前会话状态...', 'info');
            
            try {
                // 检查localStorage中的会话数据
                const encryptedSession = localStorage.getItem('ota_encrypted_session');
                const sessionMetadata = localStorage.getItem('ota_session_meta');
                const lastLoginEmail = localStorage.getItem('ota_last_login_email');
                
                if (encryptedSession && sessionMetadata) {
                    const metadata = JSON.parse(sessionMetadata);
                    const isValid = Date.now() < metadata.expiryTime;
                    
                    updateStatus('sessionStatus', isValid ? 'success' : 'error', 
                        isValid ? '会话有效' : '会话已过期');
                    updateStatus('persistenceStatus', 'success', '数据已保存');
                    
                    log(`✅ 找到会话数据: ${metadata.email}`, 'success');
                    log(`📅 登录时间: ${new Date(metadata.loginTime).toLocaleString()}`, 'info');
                    log(`⏰ 过期时间: ${new Date(metadata.expiryTime).toLocaleString()}`, 'info');
                    log(`🔄 记住我: ${metadata.rememberMe ? '是' : '否'}`, 'info');
                    
                } else {
                    updateStatus('sessionStatus', 'error', '无会话');
                    updateStatus('persistenceStatus', 'error', '无数据');
                    log('❌ 未找到会话数据', 'warning');
                }
                
                if (lastLoginEmail) {
                    log(`📧 最后登录邮箱: ${lastLoginEmail}`, 'info');
                }
                
            } catch (error) {
                log(`❌ 检查会话状态失败: ${error.message}`, 'error');
                updateStatus('sessionStatus', 'error', '检查失败');
                updateStatus('persistenceStatus', 'error', '检查失败');
            }
        }

        // 清除所有会话
        function clearAllSessions() {
            log('🧹 清除所有会话数据...', 'info');
            
            try {
                localStorage.removeItem('ota_encrypted_session');
                localStorage.removeItem('ota_session_meta');
                localStorage.removeItem('ota_last_login_email');
                
                updateStatus('sessionStatus', 'error', '无会话');
                updateStatus('persistenceStatus', 'error', '无数据');
                
                log('✅ 所有会话数据已清除', 'success');
                
            } catch (error) {
                log(`❌ 清除会话失败: ${error.message}`, 'error');
            }
        }

        // 模拟登录
        function simulateLogin() {
            const email = document.getElementById('testEmail').value;
            const rememberMe = document.getElementById('testRememberMe').checked;
            
            if (!email) {
                log('❌ 请输入测试邮箱', 'error');
                return;
            }
            
            log(`🔐 模拟登录: ${email}`, 'info');
            log(`🔄 记住我: ${rememberMe ? '是' : '否'}`, 'info');
            
            try {
                // 模拟会话数据
                const now = Date.now();
                const expiryTime = rememberMe 
                    ? now + (7 * 24 * 60 * 60 * 1000)  // 7天
                    : now + (24 * 60 * 60 * 1000);     // 24小时
                
                const sessionData = {
                    token: 'test_token_' + Math.random().toString(36).substr(2, 9),
                    userInfo: { email: email, name: '测试用户' },
                    email: email,
                    loginTime: now,
                    expiryTime: expiryTime,
                    rememberMe: rememberMe,
                    lastActivity: now,
                    version: '1.0.0'
                };
                
                // 简单加密（模拟）
                const encryptedSession = btoa(JSON.stringify(sessionData));
                
                // 保存到localStorage
                localStorage.setItem('ota_encrypted_session', encryptedSession);
                localStorage.setItem('ota_session_meta', JSON.stringify({
                    email: email,
                    loginTime: now,
                    expiryTime: expiryTime,
                    rememberMe: rememberMe,
                    hasSession: true
                }));
                localStorage.setItem('ota_last_login_email', email);
                
                updateStatus('sessionStatus', 'success', '已登录');
                updateStatus('persistenceStatus', 'success', '已保存');
                
                log('✅ 模拟登录成功', 'success');
                log(`📅 会话有效期: ${new Date(expiryTime).toLocaleString()}`, 'info');
                
            } catch (error) {
                log(`❌ 模拟登录失败: ${error.message}`, 'error');
            }
        }

        // 模拟登出
        function simulateLogout() {
            log('🚪 模拟登出...', 'info');
            clearAllSessions();
            log('✅ 模拟登出完成', 'success');
        }

        // 测试会话恢复
        function testSessionRestore() {
            log('🔄 测试会话恢复功能...', 'info');
            
            // 模拟页面加载时的会话恢复逻辑
            setTimeout(() => {
                checkSessionStatus();
                log('✅ 会话恢复测试完成', 'success');
            }, 1000);
        }

        // 刷新页面测试
        function refreshPage() {
            log('🔄 即将刷新页面测试会话恢复...', 'warning');
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }

        // 打开新标签页
        function openNewTab() {
            log('🆕 打开新标签页...', 'info');
            window.open(window.location.href, '_blank');
            log('💡 请在新标签页中测试会话状态', 'warning');
        }

        // 测试跨标签页同步
        function testCrossTabSync() {
            log('🔄 测试跨标签页同步...', 'info');
            
            // 监听localStorage变化
            window.addEventListener('storage', (event) => {
                if (event.key === 'ota_session_meta') {
                    log('📡 检测到其他标签页的会话变化', 'info');
                    checkSessionStatus();
                }
            });
            
            log('👂 已开始监听其他标签页的会话变化', 'success');
            log('💡 请在其他标签页中登录或登出以测试同步', 'warning');
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            log('🎯 会话持久化测试工具初始化完成', 'success');
            log('📋 功能说明:', 'info');
            log('  1. 检查会话状态 - 查看当前保存的会话数据', 'info');
            log('  2. 模拟登录 - 测试会话保存功能', 'info');
            log('  3. 会话恢复 - 测试页面刷新后的恢复', 'info');
            log('  4. 多标签页同步 - 测试跨标签页状态同步', 'info');
            log('🔍 开始检查当前会话状态...', 'warning');
            
            // 自动检查当前状态
            setTimeout(checkSessionStatus, 1000);
        });
    </script>
</body>
</html>
