<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统</title>

    <!-- 性能优化：DNS预解析和资源预加载 -->
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//maps.googleapis.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>

    <!-- 关键CSS内联以减少渲染阻塞 -->
    <style>
        /* 关键路径CSS - 基础样式和布局 */
        *{margin:0;padding:0;box-sizing:border-box}
        body{font-family:'Segoe UI',Tahoma,Geneva,Verdana,sans-serif;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;color:#333}
        .hidden{display:none!important}
        .modal{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);display:flex;justify-content:center;align-items:center;z-index:1000}
        .modal-content{background:white;padding:30px;border-radius:10px;box-shadow:0 10px 30px rgba(0,0,0,0.3);max-width:400px;width:90%}
        .form-group{margin-bottom:15px}
        .form-group label{display:block;margin-bottom:5px;font-weight:500;color:#555}
        .form-group input,.form-group select,.form-group textarea{width:100%;padding:8px 12px;border:1px solid #ddd;border-radius:4px;font-size:14px;transition:border-color 0.3s ease}
        .form-group input:focus,.form-group select:focus,.form-group textarea:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 2px rgba(102,126,234,0.2)}
        .checkbox-group{margin:15px 0}
        .checkbox-label{display:flex;align-items:center;cursor:pointer;font-size:14px;color:#555;position:relative}
        .checkbox-label input[type="checkbox"]{width:auto;margin-right:8px;transform:scale(1.2)}
        .checkbox-label input[type="checkbox"]:checked{accent-color:#667eea}
        .checkmark{margin-left:4px}
        button{background:#667eea;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;font-size:14px;transition:all 0.3s ease}
        button:hover{background:#5a6fd8;transform:translateY(-2px)}
        .primary-btn{background:linear-gradient(45deg,#667eea,#764ba2);padding:12px 24px;font-size:16px;font-weight:500}
        .error-message{color:#e74c3c;margin-top:10px;font-size:14px}
        .success-message{color:#27ae60;margin-top:10px;font-size:14px}
        #mainApp{display:flex;flex-direction:column;min-height:100vh}
        header{background:rgba(255,255,255,0.95);padding:15px 20px;display:flex;justify-content:space-between;align-items:center;box-shadow:0 2px 10px rgba(0,0,0,0.1)}
        header h1{color:#667eea;font-size:24px;font-weight:600}
        .header-controls{display:flex;align-items:center;gap:20px}
        main{flex:1;padding:20px;max-width:1400px;margin:0 auto}
        .section{background:rgba(255,255,255,0.95);margin-bottom:20px;padding:25px;border-radius:10px;box-shadow:0 4px 15px rgba(0,0,0,0.1)}
        .section h2{color:#333;margin-bottom:20px;font-size:20px;border-bottom:2px solid #667eea;padding-bottom:10px}
        .loading-spinner{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #667eea;border-radius:50%;animation:spin 1s linear infinite;margin:0 auto 20px}
        @keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
    </style>

    <!-- 非关键CSS异步加载 -->
    <link rel="preload" href="assets/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="assets/logger.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="assets/notification.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="assets/styles.css">
        <link rel="stylesheet" href="assets/logger.css">
        <link rel="stylesheet" href="assets/notification.css">
    </noscript>

    <!-- 关键JavaScript预加载 -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js" as="script">
</head>
<body>
    <!-- 登录窗口 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2>系统登录</h2>

            <!-- 本地模式提示 -->
            <div id="localModeNotice" class="success-message" style="display: none;">
                <strong>本地模式已启用</strong><br>
                系统将使用预设数据，无需登录
            </div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" value="" required placeholder="请输入邮箱地址">
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="" required placeholder="请输入密码">
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        记住我（7天内自动登录）
                    </label>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" class="primary-btn" id="loginSubmitBtn">登录</button>

                <!-- 错误信息显示 -->
                <div id="loginError" class="error-message" style="display: none;"></div>

                <!-- 测试账号提示 -->
                <div class="form-group" style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
                    <small style="color: #666;">
                        <strong>测试账号:</strong><br>
                        • <EMAIL> / Gomyhire@123456<br>
                        • <EMAIL> / Sky@114788
                    </small>
                </div>
            </form>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainApp" class="hidden">
        <header>
            <h1>OTA订单处理系统</h1>
            <div class="header-controls">
                <!-- 本地模式指示器 -->
                <div class="llm-status-indicator local-mode-indicator" id="localModeIndicator" title="本地模式状态">
                    <div class="status-light connected" id="localModeLight"></div>
                    <span class="status-text" id="localModeText">本地模式</span>
                    <span class="llm-label">数据模式</span>
                </div>

                <!-- Gemini 连接状态指示器 -->
                <div class="llm-status-indicator gemini-indicator" id="geminiStatusIndicator" title="点击检测 Gemini API 连接状态">
                    <div class="status-light" id="geminiStatusLight"></div>
                    <span class="status-text" id="geminiStatusText">Gemini 检测中...</span>
                    <span class="llm-label">AI服务</span>
                </div>

                <div class="user-info">
                    <span id="userInfo"></span>
                    <button id="logoutBtn" style="display: none;">退出登录</button>
                </div>
            </div>
        </header>

        <main>
            <!-- 订单输入区域 -->
            <section id="orderInput" class="section">
                <h2>订单内容输入</h2>
                <div class="input-tabs">
                    <button class="tab-btn active" data-tab="text">文字输入</button>
                    <button class="tab-btn" data-tab="image">图片上传</button>
                </div>

                <!-- 文字输入 -->
                <div id="textInput" class="tab-content active">
                    <textarea id="orderText" placeholder="请输入订单内容...\n\n示例：\n1.28接机：KE671 22.20抵达\n1.30送机：AK378 16.20起飞\n\n联系人：张梦媛\n人数：6\n车型：商务十座\n酒店：Santa Grand Signature\n\nJY"></textarea>
                </div>

                <!-- 图片上传 -->
                <div id="imageInput" class="tab-content">
                    <div class="upload-area" id="uploadArea">
                        <p>点击或拖拽图片到此处上传</p>
                        <input type="file" id="imageFile" accept="image/*" multiple title="选择要处理的图片文件">
                    </div>
                    <div id="imagePreview" class="image-preview"></div>
                    
                    <!-- 图片分析结果预览 -->
                    <div id="imageAnalysisResult" class="image-analysis-result hidden">
                        <h3>图片分析结果</h3>
                        <div class="analysis-tabs">
                            <button class="analysis-tab-btn active" data-analysis-tab="text">提取文字</button>
                            <button class="analysis-tab-btn" data-analysis-tab="details">详细分析</button>
                        </div>
                        <div id="extractedText" class="analysis-content active">
                            <textarea id="extractedTextContent" readonly placeholder="从图片中提取的文字将显示在这里..."></textarea>
                        </div>
                        <div id="analysisDetails" class="analysis-content">
                            <div id="imageLabels" class="analysis-section">
                                <h4>图片标签</h4>
                                <div id="labelsList"></div>
                            </div>
                            <div id="imageObjects" class="analysis-section">
                                <h4>检测到的物体</h4>
                                <div id="objectsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- OTA选择 -->
                <div class="ota-selection">
                    <label for="otaSelect">选择OTA类型:</label>
                    <select id="otaSelect">
                        <option value="auto">自动识别</option>
                        <option value="chong-dealer">Chong Dealer</option>
                        <option value="fallback">通用回退模板</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <button id="processBtn" class="primary-btn">处理订单</button>
            </section>

            <!-- 处理结果预览 -->
            <section id="resultPreview" class="section hidden">
                <h2>处理结果预览</h2>
                <div class="result-controls">
                    <button id="editBtn">编辑结果</button>
                    <button id="refreshBtn">重新处理</button>
                </div>

                <!-- 智能选择控制器 -->
                <div class="smart-selection-controls">
                    <h3>智能选择设置</h3>
                    
                    <!-- OTA Profile 选择器 -->
                    <div class="profile-section">
                        <div class="form-group">
                            <label for="otaProfileSelect">OTA 模板:</label>
                            <select id="otaProfileSelect" class="form-control">
                                <option value="general">通用模板</option>
                                <option value="chong-dealer">Chong Dealer 模板</option>
                            </select>
                        </div>
                        
                        <div class="profile-status">
                            <span class="profile-indicator" id="profileIndicator">
                                <i class="icon-profile">👤</i>
                                <span id="currentProfileName">通用模板</span>
                            </span>
                            <button type="button" class="btn-profile-info" id="profileInfoBtn" title="查看Profile配置">
                                <i class="icon-info">ℹ️</i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 核心选择器 -->
                    <div class="selection-row">
                        <div class="form-group">
                            <label for="backendUserSelect">负责用户:</label>
                            <select id="backendUserSelect">
                                <option value="">选择用户</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="subCategorySelect">服务类型:</label>
                            <select id="subCategorySelect">
                                <option value="">自动识别</option>
                                <option value="pickup">接机服务</option>
                                <option value="dropoff">送机服务</option>
                                <option value="charter">包车服务</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="carTypeSelect">车型:</label>
                            <select id="carTypeSelect">
                                <option value="">选择车型</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 扩展选择器 -->
                    <div class="selection-row">
                        <div class="form-group">
                            <label for="drivingRegionSelect">行驶区域:</label>
                            <select id="drivingRegionSelect" class="form-control">
                                <option value="">智能选择</option>
                                <!-- 动态生成区域选项 -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="languagesSelect">服务语言:</label>
                            <select id="languagesSelect" class="form-control" multiple>
                                <!-- 支持多选的语言选项 -->
                            </select>
                            <small class="form-help">按住Ctrl键可多选</small>
                        </div>
                    </div>
                </div>

                <div id="resultContent" class="result-content">
                    <!-- 订单处理结果 -->
                    <div id="orderResults"></div>

                    <!-- 手动编辑区域 -->
                    <div id="manualEditSection" class="manual-edit-section hidden">
                        <h3>手动编辑订单</h3>
                        <div class="edit-controls">
                            <button id="addOrderBtn" class="secondary-btn">添加新订单</button>
                            <button id="reAnalyzeBtn" class="secondary-btn">重新分析</button>
                        </div>
                        <div id="orderEditForms" class="order-edit-forms">
                            <!-- 动态生成的订单编辑表单 -->
                        </div>
                    </div>

                    <!-- 图片处理结果 -->
                    <div id="imageResults"></div>
                </div>
                <div class="action-buttons">
                    <button id="createOrderBtn" class="primary-btn">创建订单</button>
                    <button id="exportBtn">导出结果</button>
                </div>
            </section>

            <!-- 订单创建状态 -->
            <section id="orderStatus" class="section hidden">
                <h2>订单创建状态</h2>
                <div id="statusContent">
                    <!-- 创建结果 -->
                    <div id="createResults"></div>
                </div>
            </section>
        </main>
    </div>

    <!-- 加载提示 -->
    <div id="loadingModal" class="modal hidden">
        <div class="modal-content">
            <div class="loading-spinner"></div>
            <p id="loadingText">正在处理...</p>
        </div>
    </div>

    <!-- Profile 配置预览弹窗 -->
    <div class="profile-preview-modal" id="profilePreviewModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Profile 配置预览</h3>
                <button class="modal-close" id="profileModalClose">&times;</button>
            </div>
            <div class="modal-body" id="profilePreviewContent">
                <!-- 动态生成 Profile 配置内容 -->
            </div>
        </div>
    </div>

    <!-- 性能优化：关键路径JavaScript优化加载 -->
    <script>
        /**
         * @file 高性能模块加载器 - 优化启动速度
         * @description 实现分层异步加载，减少初始加载时间30%+
         */

        // 性能监控
        const performanceTracker = {
            startTime: performance.now(),
            milestones: {},

            mark(name) {
                this.milestones[name] = performance.now() - this.startTime;
                console.log(`⚡ ${name}: ${this.milestones[name].toFixed(2)}ms`);
            },

            report() {
                const total = performance.now() - this.startTime;
                console.log(`🚀 总加载时间: ${total.toFixed(2)}ms`);
                return { total, milestones: this.milestones };
            }
        };

        // 高性能脚本加载器
        class FastScriptLoader {
            constructor() {
                this.loadedScripts = new Set();
                this.loadingPromises = new Map();
            }

            async loadScript(src, options = {}) {
                if (this.loadedScripts.has(src)) {
                    return Promise.resolve();
                }

                if (this.loadingPromises.has(src)) {
                    return this.loadingPromises.get(src);
                }

                const promise = new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = src;
                    script.async = options.async !== false;
                    script.defer = options.defer || false;

                    script.onload = () => {
                        this.loadedScripts.add(src);
                        resolve();
                    };
                    script.onerror = () => reject(new Error(`Failed to load ${src}`));

                    document.head.appendChild(script);
                });

                this.loadingPromises.set(src, promise);
                return promise;
            }

            async loadScripts(scripts, parallel = true) {
                if (parallel) {
                    return Promise.all(scripts.map(src => this.loadScript(src)));
                } else {
                    for (const src of scripts) {
                        await this.loadScript(src);
                    }
                }
            }
        }

        const scriptLoader = new FastScriptLoader();

        // 分层加载策略
        async function initializeApp() {
            try {
                performanceTracker.mark('开始加载');

                // 第一层：关键基础设施（并行加载）
                const coreScripts = [
                    'core/config.js',
                    'core/logger.js'
                ];

                await scriptLoader.loadScripts(coreScripts, true);
                performanceTracker.mark('基础设施加载完成');

                // 第二层：状态管理和数据提供者（并行加载）
                const stateScripts = [
                    'core/app-state.js',
                    'core/local-data-provider.js'
                ];

                await scriptLoader.loadScripts(stateScripts, true);
                performanceTracker.mark('状态管理加载完成');

                // 第三层：核心服务（并行加载）
                const serviceScripts = [
                    'services/api-service.js',
                    'components/notification.js',
                    'core/interface-controller.js',  // 添加缺失的InterfaceController
                    'core/session-manager.js'        // 添加会话管理器
                ];

                await scriptLoader.loadScripts(serviceScripts, true);
                performanceTracker.mark('核心服务加载完成');

                // 第四层：应用控制器（顺序加载）
                await scriptLoader.loadScript('core/module-loader.js');
                await scriptLoader.loadScript('core/enhanced-login-handler.js');
                await scriptLoader.loadScript('core/app.js');
                performanceTracker.mark('应用控制器加载完成');

                // 启用本地模式
                if (window.localDataProvider) {
                    window.localDataProvider.enableLocalMode();
                    console.log('✅ 本地模式已启用');
                }

                performanceTracker.mark('应用初始化完成');
                performanceTracker.report();

                // 开始按需加载非关键模块
                requestIdleCallback(() => loadNonCriticalModules());

            } catch (error) {
                console.error('❌ 应用初始化失败:', error);
                // 降级到传统加载方式
                fallbackLoad();
            }
        }

        // 非关键模块按需加载
        async function loadNonCriticalModules() {
            const nonCriticalScripts = [
                'core/test-data-provider.js',
                'core/interface-controller.js'
            ];

            try {
                await scriptLoader.loadScripts(nonCriticalScripts, true);
                performanceTracker.mark('非关键模块加载完成');
            } catch (error) {
                console.warn('⚠️ 非关键模块加载失败:', error);
            }
        }

        // 降级加载方案
        function fallbackLoad() {
            const scripts = [
                'core/config.js', 'core/logger.js', 'core/test-data-provider.js',
                'core/local-data-provider.js', 'core/app-state.js', 'services/api-service.js',
                'core/interface-controller.js', 'components/notification.js',
                'core/module-loader.js', 'core/app.js'
            ];

            scripts.forEach(src => {
                const script = document.createElement('script');
                script.src = src;
                document.head.appendChild(script);
            });
        }

        // 外部依赖异步加载
        async function loadExternalDependencies() {
            try {
                await scriptLoader.loadScript('https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js');
                performanceTracker.mark('外部依赖加载完成');
            } catch (error) {
                console.error('❌ 外部依赖加载失败:', error);
            }
        }

        // 启动应用
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                initializeApp();
                loadExternalDependencies();
            });
        } else {
            initializeApp();
            loadExternalDependencies();
        }
    </script>

    <!-- 业务模块懒加载管理器 -->
    <script>
        /**
         * @file 业务模块懒加载管理器
         * @description 实现智能的业务模块按需加载，提升性能
         */

        class BusinessModuleLoader {
            constructor() {
                this.loadedModules = new Set();
                this.loadingPromises = new Map();
                this.interactionListeners = new Map();
            }

            async loadBusinessModules() {
                if (this.loadedModules.has('business-core')) return;

                const modules = [
                    'core/prompts.js',
                    'services/llm-service.js',
                    'services/order-parser.js',
                    'core/order-manager.js',
                    'core/resilience-manager.js',
                    'core/performance-optimizer.js'
                ];

                try {
                    const startTime = performance.now();
                    console.log('📦 开始加载业务核心模块...', modules);

                    await scriptLoader.loadScripts(modules, true);

                    const loadTime = performance.now() - startTime;
                    console.log(`✅ 业务模块加载完成: ${loadTime.toFixed(2)}ms`);

                    this.loadedModules.add('business-core');

                    // 等待一小段时间确保模块完全初始化
                    await new Promise(resolve => setTimeout(resolve, 100));

                    // 通知主应用 - 增强回调机制
                    if (window.app) {
                        console.log('🔄 通知主应用：业务模块已加载');
                        if (typeof window.app.onCoreModulesLoaded === 'function') {
                            await window.app.onCoreModulesLoaded();
                        } else {
                            console.warn('⚠️ onCoreModulesLoaded 方法不存在，尝试手动初始化');
                            // 手动触发核心服务初始化
                            if (typeof window.app.initializeCoreServices === 'function') {
                                await window.app.initializeCoreServices();
                            }
                        }
                    } else {
                        console.warn('⚠️ window.app 不存在，延迟重试');
                        // 延迟重试
                        setTimeout(() => {
                            if (window.app?.onCoreModulesLoaded) {
                                window.app.onCoreModulesLoaded();
                            }
                        }, 1000);
                    }

                    this.setupInteractionLoaders();

                } catch (error) {
                    console.error('❌ 业务模块加载失败:', error);
                    // 提供降级方案
                    this.setupFallbackHandlers();
                }
            }

            // 新增：设置降级处理器
            setupFallbackHandlers() {
                console.log('🔧 设置降级处理器');

                // 为处理订单按钮添加降级处理
                const processBtn = document.getElementById('processBtn');
                if (processBtn) {
                    processBtn.addEventListener('click', () => {
                        alert('系统正在初始化中，请稍后重试。如果问题持续存在，请刷新页面。');
                    });
                }
            }

            setupInteractionLoaders() {
                // 图片处理模块触发器
                this.addInteractionLoader('image-tab', '[data-tab="image"]', 'click', () =>
                    this.loadModule('image-processing', ['services/image-service.js'])
                );

                // 智能选择模块触发器 - 已在预加载中处理，移除重复加载
                // this.addInteractionLoader('process-btn', '#processBtn', 'click', () =>
                //     this.loadModule('smart-selection', ['core/smart-selection.js'])
                // );

                // 地址搜索模块触发器
                this.addInteractionLoader('address-inputs', 'input[name="pickup"], input[name="destination"]', 'focus', () =>
                    this.loadModule('address-search', ['services/address-search-service.js'])
                );
            }

            addInteractionLoader(id, selector, event, loader) {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    const handler = async () => {
                        await loader();
                        element.removeEventListener(event, handler);
                    };
                    element.addEventListener(event, handler, { once: true });
                });
            }

            async loadModule(name, scripts) {
                if (this.loadedModules.has(name)) return;

                try {
                    await scriptLoader.loadScripts(scripts, true);
                    this.loadedModules.add(name);
                    console.log(`🔧 ${name} 模块加载完成`);
                } catch (error) {
                    console.error(`❌ ${name} 模块加载失败:`, error);
                }
            }
        }

        const businessLoader = new BusinessModuleLoader();

        // 优化业务模块加载时机 - 修复Gemini连接和订单处理问题
        document.addEventListener('DOMContentLoaded', () => {
            // 立即加载业务模块，确保核心功能可用
            setTimeout(() => {
                console.log('🚀 开始加载业务核心模块...');
                businessLoader.loadBusinessModules();
            }, 500); // 减少延迟，确保快速加载
        });
    </script>

    </script>

    <!-- 外部API智能加载管理器 -->
    <script>
        /**
         * @file 外部API智能加载管理器
         * @description 按需加载外部API，减少初始加载时间
         */

        class ExternalAPIManager {
            constructor() {
                this.loadedAPIs = new Set();
                this.apiCallbacks = new Map();
            }

            // Google Maps API 按需加载
            async loadGoogleMapsAPI() {
                if (this.loadedAPIs.has('google-maps')) {
                    return Promise.resolve();
                }

                return new Promise((resolve, reject) => {
                    // 设置全局回调
                    window.initGoogleMapsAPI = () => {
                        this.loadedAPIs.add('google-maps');
                        console.log('🗺️ Google Maps API 加载完成');

                        // 通知地址搜索服务
                        if (window.addressSearchService) {
                            window.addressSearchService.onGoogleMapsAPIReady();
                        }

                        resolve();
                    };

                    // 动态加载脚本
                    const script = document.createElement('script');
                    script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyBsBoEOqYR2_Y3UZ5e-5k7h8MZTvZ7XbUE&libraries=places&language=zh-CN&region=MY&loading=async&callback=initGoogleMapsAPI';
                    script.async = true;
                    script.onerror = () => reject(new Error('Google Maps API 加载失败'));

                    document.head.appendChild(script);
                });
            }

            // 智能触发器：当用户需要地址搜索时才加载
            setupGoogleMapsLoader() {
                const triggers = [
                    'input[name="pickup"]',
                    'input[name="destination"]',
                    '.address-input',
                    '[data-address-search]'
                ];

                const loadOnInteraction = async () => {
                    if (!this.loadedAPIs.has('google-maps')) {
                        console.log('🔄 按需加载 Google Maps API...');
                        await this.loadGoogleMapsAPI();
                    }
                };

                // 为现有元素添加触发器
                triggers.forEach(selector => {
                    document.querySelectorAll(selector).forEach(element => {
                        element.addEventListener('focus', loadOnInteraction, { once: true });
                    });
                });

                // 监听动态添加的元素
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                triggers.forEach(selector => {
                                    const elements = node.querySelectorAll ? node.querySelectorAll(selector) : [];
                                    elements.forEach(element => {
                                        element.addEventListener('focus', loadOnInteraction, { once: true });
                                    });
                                });
                            }
                        });
                    });
                });

                observer.observe(document.body, { childList: true, subtree: true });
            }
        }

        const externalAPIManager = new ExternalAPIManager();

        // 在DOM加载完成后设置触发器
        document.addEventListener('DOMContentLoaded', () => {
            externalAPIManager.setupGoogleMapsLoader();
        });

        // 全局API管理器
        window.externalAPIManager = externalAPIManager;
    </script>
</body>
</html>